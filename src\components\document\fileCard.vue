<template>
  <div>
    <div v-if="!fileSelect" class="file-card">
      <div class="file-card--image">
        <img v-if="thumbnail_url" :src="thumbnail_url" alt="" />
        <img v-else src="~@/assets/images/thumbnail-placeholder.png" />
      </div>
      <div
        v-if="showFileActionCard"
        class="file-card-action"
      >
        <img
          src="~@/assets/images/icons/more-icon.svg"
          class="file-card-action--btn"
          alt=""
        />
        <div class="file-card-options">
          <div
            v-if="disableDownload()"
            class="file-card-option"
            @click="disable"
            :data-enable="disableDownload()"
          >
            <span :class="{ 'grey-text' : disabledisableDownloadButton }">Disable Download</span>
          </div>
          <div
            v-if="enableDownload()"
            class="file-card-option"
            @click="enable"
            :data-enable="enableDownload()"
          >
            <span :class="{ 'grey-text' : disableEnableDownloadButton }">Enable Download</span>
          </div>
          <div
            v-if="canCheckin"
            class="file-card-option"
            @click="checkin"
            :data-enable="canCheckin"
          >
            <span :class="{ 'grey-text' : disableCheckinButton }"> Checkin </span>
          </div>
          <div
            v-else-if="canCheckout"
            class="file-card-option"
            @click="checkout"
            :data-enable="canCheckout"
          >
            <span :class="{ 'grey-text' : disableCheckoutButton }">Checkout</span>
          </div>
          <div
        v-if="canUndoCheckout"
          class="file-card-option"
          @click="undoCheckout"
          :data-enable="canUndoCheckout"
        >
          <span :class="{ 'grey-text' : disableUndoCheckoutButton }">Undo Checkout</span>
        </div>
        <div>
            <div
              v-if="canLock"
              class="file-card-option"
              @click="lock"
              :data-enable="canLock"
            >
              <span :class="{ 'grey-text' : disableLockButton }">Lock</span>
            </div>
            <div
              v-else-if="(file.state === META_CONFIG.DOC_STATE_MAP.LOCK && this.user.tenantLevelRole !== 'EDITOR')"
              class="file-card-option"
              @click="unLock"
              :data-enable="true"
            >
              <span :class="{ 'grey-text' : disableUnlockButton }">Unlock</span>
            </div>
          </div>
          <div v-if="showObseleteOption">
            <div
              class="file-card-option"
              @click="obselete"
              v-if="canObselete && !collaborator"
              :data-enable="canObselete"
            >
              <span :class="{ 'grey-text' : disableObseleteButton }">Obselete</span>
            </div>
            <div
              v-else-if="file.state === META_CONFIG.DOC_STATE_MAP.OBSOLETE"
              @click="nonObselete"
              class="file-card-option"
              :data-enable="!canObselete"
            >
              <span :class="{ 'grey-text' : disableNonObseleteButton }">Non Obselete</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="file?.inherited_from_doc_id" class="file-card-action">
        <img
          v-tooltip="InheritedIconTooltip"
          src="~@/assets/images/icons/inherit-icon.svg"
          class="file-card-action--btn"
          @click="openUpdateInheritedDocModal"
          alt=""
        />
      </div>
      <div class="p-2">
        <div
          class="weight-500 l elipsis-text"
          v-overflow-tooltip
          :class="isObselete ? 'red' : ''"
        >
          {{ updatedName ? updatedName : file.doc_name }}
        </div>
        <div class="mt-3 v-center">
          <img
            src="~@/assets/images/icons/material-symbols.svg"
            class="document-view-icons"
            alt="material-symbol"
          />
          <div class="weight-400 xs ml-1">Versions:</div>
          <div class="chip" :class="isObselete ? 'obsolete' : ''">
            {{
              file?.inherited_from_doc_id
                ? file?.inherited_from_document?.associated_versions_aggregate
                    ?.aggregate?.count + 1
                : file?.associated_versions_aggregate?.aggregate?.count + 1
            }}
            Versions
          </div>
        </div>
        <div class="mt-3 v-center">
        <div class="weight-400 xs ml-1">PLM Id:</div>
        <div class="chip">
            {{ file?.plm_document_id ?? '--' }} {{ `(version: ${file?.plm_version_no ?? '--'})` }}
        </div>
        </div>
        <div class="mt-3 v-center">
          <img
            src="~@/assets/images/icons/state-icon.svg"
            class="document-view-icons"
            alt="material-symbol"
            width="15"
          />
          <div class="weight-400 xs ml-1">State:</div>
          <div class="chip" :class="isObselete ? 'obsolete' : ''">
            {{
              META_CONFIG.DOC_STATE[
                file?.inherited_from_doc_id
                  ? file?.inherited_from_document?.state
                  : file.state
              ]
            }}
            {{ findwhocheckout ? 'by '+findwhocheckout : null}}
          </div>
        </div>
        <div class="mt-3 v-center flex-end file-card-footer">
          <upload-document
            :folder="file"
            :open="false"
            :parentFolder="parentFolder"
            :is_revision="true"
            @uploaded="uploadedRevision"
            v-if="canUpload"
          >
            <div
              v-if="showUploadRevisionButton"
              class="icon-btn"
              v-tooltip="'Upload revision'"
            >
              <img src="~@/assets/images/icons/upload-icon.svg" alt="" />
            </div>
          </upload-document>
          <div
            v-if="checkShareable"
            @click="shareDocs = true"
            class="icon-btn-document"
            v-tooltip="'Share Document'"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 -960 960 960"
              width="22"
            >
              <path
                d="M720-80q-50 0-85-35t-35-85q0-7 1-14.5t3-13.5L322-392q-17 15-38 23.5t-44 8.5q-50 0-85-35t-35-85q0-50 35-85t85-35q23 0 44 8.5t38 23.5l282-164q-2-6-3-13.5t-1-14.5q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35q-23 0-44-8.5T638-672L356-508q2 6 3 13.5t1 14.5q0 7-1 14.5t-3 13.5l282 164q17-15 38-23.5t44-8.5q50 0 85 35t35 85q0 50-35 85t-85 35Zm0-640q17 0 28.5-11.5T760-760q0-17-11.5-28.5T720-800q-17 0-28.5 11.5T680-760q0 17 11.5 28.5T720-720ZM240-440q17 0 28.5-11.5T280-480q0-17-11.5-28.5T240-520q-17 0-28.5 11.5T200-480q0 17 11.5 28.5T240-440Zm480 280q17 0 28.5-11.5T760-200q0-17-11.5-28.5T720-240q-17 0-28.5 11.5T680-200q0 17 11.5 28.5T720-160Zm0-600ZM240-480Zm480 280Z"
              />
            </svg>
            <!-- <img src="~@/assets/images/icons/photo-icon.svg" alt="" /> -->
          </div>
          <div
            v-if="canViewDetail"
            @click="openDetail"
            class="icon-btn"
            v-tooltip="'View Detail'"
          >
            <img src="~@/assets/images/icons/photo-icon.svg" alt="" />
          </div>
          <div v-if="showDeleteButton">
            <div
              v-if="canDelete && !collaborator"
              @click="tryDeleteFile"
              class="icon-btn"
              v-tooltip="'Delete'"
            >
              <img src="~@/assets/images/delete-gray-icon.svg" alt="" />
            </div>
          </div>
          <div>
            <div
              class="icon-btn"
              @click="editDocModal = true"
              v-if="canRename"
              v-tooltip="'Rename'"
            >
              <img src="~@/assets/images/edit-icon.svg" alt="" />
            </div>
          </div>
          <spinner v-if="isDownloading"></spinner>
          <div v-else-if="viewOnly"></div>
          <div v-else-if="!collaborator" class="icon-btn download-doc-icon" ref="downloadBox">
            <img
              src="~@/assets/images/icons/cloud-download.svg"
              class="document-view-icons"
              alt="material-symbol"
              @click="openDownLoadList"
              v-tooltip="'Download'"
            />
            <div
              class="download-doc-icon-list"
              v-if="openVersionList"
              @mouseleave="openVersionList = false"
            >
              <ul>
                <li
                  v-for="version in fileVersions"
                  :key="version.blob_key"
                  @click="downloadSelectedFile(version)"
                >
                  Version - {{ version.version_no }}
                </li>
              </ul>
            </div>
          </div>
          <div v-else-if="collaborator && !file.view_only " class="icon-btn download-doc-icon" ref="downloadBox">
            <img
              src="~@/assets/images/icons/cloud-download.svg"
              class="document-view-icons"
              alt="material-symbol"
              @click="openDownLoadList"
              v-tooltip="'Download'"
            />
            <div
              class="download-doc-icon-list"
              v-if="openVersionList"
              @mouseleave="openVersionList = false"
            >
              <ul>
                <li
                  v-for="version in fileVersions"
                  :key="version.blob_key"
                  @click="downloadSelectedFile(version)"
                >
                  Version - {{ version.version_no }}
                </li>
              </ul>
            </div>
          </div>
          <div class="icon-btn download-doc-icon" v-else-if="collaborator && file.view_only">
            <img v-tooltip="'Disabled Download'" src="~@/assets/images/disable_download.png" alt="">
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="file-name-card" @click="$emit('file-selected', file)">
        <input
          type="checkbox"
          :checked="file.id === selectedFile?.id ? true : false"
        />
        <img src="~@/assets/images/icons/file-icon.svg" width="20px" alt="" />
        <span class="weight-500 l elipsis-text" v-overflow-tooltip>
          {{
            file?.inherited_from_doc_id
              ? file?.inherited_from_document?.doc_name
              : file.doc_name
          }}
        </span>
      </div>
    </div>
    <modal
      title="Edit Document"
      :open="editDocModal"
      @close="editDocModal = false"
    >
      <rename-dialog
        @cancel="editDocModal = false"
        @renamed="handleRename"
        :updated-name="updatedName"
        :file="file"
        :open="editDocModal"
        :image="thumbnail_url"
        v-if="editDocModal"
      />
    </modal>
    <modal
      title="Update Document"
      :open="updateInheritedDocModal"
      @close="updateInheritedDocModal = false"
      :closeOnOutsideClick="true"
    >
      <div class="p-5">
        Are you sure to update the document to latest ?
        <div class="flex-end mt-7">
          <button
            class="btn btn-black ml-3"
            @click="updateInheritedDocModal = false"
          >
            Cancel
          </button>
          <button class="btn ml-3 update-btn" @click="updateInheritedDoc">
            Update
          </button>
        </div>
      </div>
    </modal>
    <modal
      v-if="!collaborator"
      title="Share Documents"
      :closeOnOutsideClick="true"
      @close="shareDocs = false"
      :open="shareDocs"
    >
      <share-document
        v-if="shareDocs"
        :id="file.id"
        :versionId="2"
        item="document"
        @close="shareDocs = false"
      />
    </modal>
  </div>
</template>

<script>
import {
  generateS3DownloadingUrlSingle,
  deleteDocument,
  setDocumentState,
  updateInheritedDocument,
  addComments,
  getAllAttachedDocs,
  SwitchDownload,
  SendNotificationsForReadingAndDownloadingDocs
  // downloadDocument
} from '@/api'
import confirmationDialog from '@/plugins/confirmationDialog'
import Modal from '../common/modal.vue'
import ShareDocument from '../common/share.vue'
import UploadDocument from './uploadDocument.vue'
import RenameDialog from './renameDialog.vue'
import Spinner from '../common/spinner.vue'
import META_CONFIG from '@/config'
import { mapGetters } from 'vuex'
import { success, alert } from '@/plugins/notification'
export default {
  components: { UploadDocument, Spinner, Modal, ShareDocument, RenameDialog },
  name: 'fileCard',
  props: {
    thumbnail_url: {
      type: String,
      default: ''
    },
    fileSelect: {
      type: Boolean,
      default: false
    },
    selectedFile: {
      type: Object,
      default: () => ({})
    },
    file: {
      type: Object,
      default: () => ({})
    },
    parentFolder: {
      type: Object,
      default: () => ({})
    }
  },
  data: () => ({
    disableCheckinButton: false,
    disableCheckoutButton: false,
    disableUndoCheckoutButton: false,
    disableLockButton: false,
    disableUnlockButton: false,
    disableObseleteButton: false,
    disableNonObseleteButton: false,
    disabledisableDownloadButton: false,
    disableEnableDownloadButton: false,
    META_CONFIG,
    openVersionList: false,
    isDownloading: false,
    shareDocs: false,
    updateInheritedDocModal: false,
    editDocModal: false,
    updatedName: '',
    attachedDocs: []
  }),
  computed: {
    ...mapGetters(['isTenantAdmin', 'isOnProjectLevel', 'isProjectViewer', 'isTenantViewer', 'childTenantsList']),
    ...mapGetters(['user', 'getUserById', 'collaborator', 'isExternalCollaborator']),
    filteredChildTenants () {
      return this.childTenantsList.filter((tenant) => ((tenant.status === 1 || tenant.status === 4) && tenant.target_tenant.status === 1))
    },
    findwhocheckout () {
      if (this.file.state !== META_CONFIG.DOC_STATE_MAP.CHECKOUT) {
        return false
      }
      if (this.file.checked_out_by === this.user.userId) {
        return 'You'
      } else {
        return this.getUserById(this.file.checked_out_by).associated_user?.first_name
      }
    },
    viewOnly () {
      // here this.isTenantViewer might come true  for ext colaborator , bcz isTenantViewer is showing the role of this user with his actual tenant, so for exr colabarotor this check will get failed
      if (this.collaborator) {
        return false
      } else if (this.isOnProjectLevel) {
        return this.isProjectViewer
      } else {
        return this.isTenantViewer
      }
    },
    canUndoCheckout () {
      if (this.file.ongoing_revision_form_id) {
        return false
      } else if (this.collaborator) {
        return (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT && this.file.checked_out_by === this.user.userId)
        // if the documnet is check out by someone else , then admin could be able to do  undo the checkout
      } else if (this.file.checked_out_by === this.user.userId) {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT
        )
      } else {
        return (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT && this.user.tenantLevelRole === 'ADMIN')
      }
    },
    canCheckin () {
      if (this.file.ongoing_revision_form_id) {
        return false
      } else if (this.collaborator) {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT && !this.file.view_only && this.file.checked_out_by === this.user.userId
        )
      } else {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT &&
         (this.file.checked_out_by === this.user.userId ||
          (
            this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR' ||
            this.user.tenantLevelRole === 'EDITOR' ||
            this.user.projectLevelRole === 'EDITOR'
          )
         ))
      }
    },
    checkShareable () {
      if (!this.filteredChildTenants.length) {
        return false
      }
      if (this.file.ongoing_revision_form_id) {
        return false
      } else if (this.isOnProjectLevel) {
        return (
          !this.collaborator &&
          this.file.state !== META_CONFIG.DOC_STATE_MAP.OBSOLETE &&
          this.user.projectLevelRole !== 'VIEWER' &&
          (!this.collaborator || !this.file.view_only)
        )
      } else {
        return (
          !this.collaborator &&
          this.file.state !== META_CONFIG.DOC_STATE_MAP.OBSOLETE &&
          this.user.tenantLevelRole !== 'VIEWER' &&
          (!this.collaborator || !this.file.view_only)
        )
      }
    },
    isObselete () {
      const state = this.file?.inherited_from_doc_id
        ? this.file?.inherited_from_document?.state
        : this.file.state
      return state === META_CONFIG.DOC_STATE_MAP.OBSOLETE
    },
    canCheckout () {
      if (this.file.ongoing_revision_form_id) {
        return false
      } else if (this.collaborator) {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKIN && !this.file.view_only
        )
      } else {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKIN &&
         (this.file.checked_out_by === this.user.userId ||
          (
            this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR' ||
            this.user.tenantLevelRole === 'EDITOR' ||
            this.user.projectLevelRole === 'EDITOR'
          )
         ))
      }
    },
    isInheritedLatest () {
      if (!this.file.inherited_from_doc_id) {
        return false
      }
      if (this.file.inherited_from_document?.associated_versions?.length) {
        return (
          this.file.inherited_from_document?.associated_versions[
            this.file.inherited_from_document?.associated_versions?.length - 1
          ].id === this.file.inherited_from_doc_id
        )
      }
      if (
        this.file.inherited_from_document.main_version_document
          ?.associated_versions?.length
      ) {
        return (
          this.file.inherited_from_document.main_version_document
            ?.associated_versions[
            this.file.inherited_from_document.main_version_document
              ?.associated_versions?.length - 1
            ]?.id === this.file.inherited_from_doc_id
        )
      }
      return true
    },
    InheritedIconTooltip () {
      if (this.isInheritedLatest) {
        return 'Document is at the latest version'
      } else if (this.user.tenantLevelRole === 'VIEWER') {
        return 'Document is outdated'
      } else {
        return 'Document is outdated. Click the icon to update'
      }
    },
    canLock () {
      if (this.file.ongoing_revision_form_id) {
        return false
      } else {
        return (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKIN && this.user.tenantLevelRole !== 'EDITOR' && !(this.collaborator || this.isExternalCollaborator))
      }
    },
    canObselete () {
      if (this.file.ongoing_revision_form_id) {
        return false
      } else if (this.user.projectLevelRole === null) {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKIN &&
          this.isTenantAdmin &&
          this.user.tenantLevelRole === 'ADMIN'
        )
      } else {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKIN &&
          this.isTenantAdmin &&
          this.user.projectLevelRole === 'ADMIN'
        )
      }
    },
    canDelete () {
      if (this.file.ongoing_revision_form_id) {
        return false
      } else if (this.user.projectLevelRole === null) {
        return (
          (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKIN ||
            (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT &&
              this.file.checked_out_by === this.user.userId)) &&
          this.user.tenantLevelRole === 'ADMIN'
        )
      } else {
        return (
          (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKIN ||
            (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT &&
              this.file.checked_out_by === this.user.userId)) &&
          this.user.projectLevelRole === 'ADMIN'
        )
      }
    },
    canUpload () {
      if (this.file.ongoing_revision_form_id) {
        return false
      } else {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKIN ||
          (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT && this.file.checked_out_by === this.user.userId)
        )
      }
    },
    canRename () {
      if (this.file.ongoing_revision_form_id) {
        return false
      } else {
        return (
          (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT && this.file.checked_out_by === this.user.userId)
        )
      }
    },
    canViewDetail () {
      return true
    },
    showFileActionCard () {
      if (this.file?.inherited_from_doc_id) {
        return false
      } else if (this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT && this.file.checked_out_by !== this.user.userId) {
        return false
      } else if (this.collaborator && !this.file.view_only) {
        if (this.file.state === META_CONFIG.DOC_STATE_MAP.OBSOLETE) {
          return false
        }
        return true
      } else if (this.isOnProjectLevel) {
        return (
          this.user.projectLevelRole === 'ADMIN' ||
          this.user.projectLevelRole === 'COLLABORATOR' ||
          this.user.projectLevelRole === 'EDITOR'
        )
      } else {
        return (
          this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'COLLABORATOR' ||
          this.user.tenantLevelRole === 'EDITOR'
        )
      }
    },
    showObseleteOption () {
      return this.user.tenantLevelRole === 'ADMIN'
    },
    showUploadRevisionButton () {
      if (this.collaborator) {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT &&
          this.file.checked_out_by === this.user.userId &&
          !this.file.view_only
        )
      } else {
        return (
          this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT &&
      (
        this.user.tenantLevelRole === 'ADMIN' ||
        this.user.tenantLevelRole === 'EDITOR' ||
        this.user.tenantLevelRole === 'COLLABORATOR' ||
        this.user.projectLevelRole === 'EDITOR'
      )
        )
      }
    },
    showDeleteButton () {
      return this.user.tenantLevelRole === 'ADMIN' && this.file.state === META_CONFIG.DOC_STATE_MAP.CHECKOUT && (!this.collaborator || !this.file.view_only)
    },
    showLockButton () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'COLLABORATOR') &&
        this.file.state !== META_CONFIG.DOC_STATE_MAP.OBSOLETE
      )
    },
    fileVersions () {
      const versions = []
      versions.push({
        created_on: this.file.created_on,
        blob_key: this.file.blob_key,
        version_no: 1,
        doc_name: this.file.doc_name,
        id: this.file.id
      })
      this.file.associated_versions.forEach((version, index) => {
        versions.push({
          created_on: version.created_on,
          blob_key: version.blob_key,
          id: version.id,
          doc_name: version.doc_name,
          version_no: index + 2
        })
      })
      return versions
    },
    getLatestDocId () {
      if (!Array.isArray(this.fileVersions) || this.fileVersions.length === 0) return null
      return this.fileVersions.length > 1 ? this.fileVersions[this.fileVersions.length - 1] : this.fileVersions[0]
    }
  },
  methods: {
    disable () {
      if (this.disabledisableDownloadButton) {
        return
      }
      this.disabledisableDownloadButton = true
      let token = 'tenant'
      if (this.isOnProjectLevel) {
        token = 'project'
      }
      SwitchDownload(this.file.id, true, token).then(res => {
        this.$emit('updated')
        success(`Download disabled for ${this.file.doc_name}`)
      }).catch(() => {
        alert('Unable to disable download')
      }).finally(() => {
        this.disabledisableDownloadButton = false
      })
    },
    enable () {
      if (this.disableEnableDownloadButton) {
        return
      }
      this.disableEnableDownloadButton = true
      let token = 'tenant'
      if (this.isOnProjectLevel) {
        token = 'project'
      }
      SwitchDownload(this.file.id, false, token).then(res => {
        this.$emit('updated')
        success(`Download enabled for ${this.file.doc_name}`)
      }).catch(() => {
        alert('Unable to enable download')
      }).finally(() => {
        this.disableEnableDownloadButton = false
      })
    },
    enableDownload () {
      if (this.file.view_only && !this.collaborator) return true
      else return false
    },
    disableDownload () {
      if (!this.file.view_only && !this.collaborator) return true
      else return false
    },
    handleRename (newName) {
      if (newName) {
        this.updatedName = newName
      }
      this.editDocModal = false
      this.$emit('updated')
    },
    openUpdateInheritedDocModal () {
      if (!this.isInheritedLatest && this.user.tenantLevelRole !== 'VIEWER') {
        this.updateInheritedDocModal = true
      }
    },
    updateInheritedDoc () {
      let latestDocId
      if (
        this.file.inherited_from_document?.associated_versions?.length !== 0
      ) {
        latestDocId =
          this.file.inherited_from_document?.associated_versions[
            this.file.inherited_from_document?.associated_versions?.length - 1
          ].id
      } else if (
        this.file.inherited_from_document.main_version_document
          ?.associated_versions?.length !== 0
      ) {
        latestDocId =
          this.file.inherited_from_document.main_version_document
            ?.associated_versions[
            this.file.inherited_from_document.main_version_document
              ?.associated_versions?.length - 1
            ].id
      }
      updateInheritedDocument(latestDocId, this.file.id)
        .then(() => {
          this.updateInheritedDocModal = false
          this.$emit('updated-inherited-doc')
          success('Successfully updated document')
        })
        .catch(() => {
          alert('Unable to update document')
        })
    },
    deleteFile (res) {
      if (res) {
        deleteDocument(this.file.id).then((res) => {
          this.$emit('delete')
        })
      }
    },
    async tryDeleteFile () {
      let confirmMessage = 'Are you sure you want to delete this file?'
      if (this.$props.file.material_id) {
        confirmMessage = 'This file is attached to a material. ' + confirmMessage
      } else if (this.isOnProjectLevel) {
        await getAllAttachedDocs().then((res) => {
          this.attachedDocs = res.task_document_association.map((item) => {
            return item
          })
        })
        const Document = this.attachedDocs.find(doc => doc.document_id === this.file.id)
        if (Document) {
          confirmMessage = 'This file is attached to a task. ' + confirmMessage
        }
      }
      confirmationDialog(
        confirmMessage,
        this.deleteFile,
        'Delete',
        'Cancel',
        'Delete File'
      )
    },
    uploadedRevision () {
      this.$emit('uploaded')
    },
    openDetail () {
      SendNotificationsForReadingAndDownloadingDocs(this.file.id, 'view')
      this.$emit('openDetail')
    },
    lock () {
      if (this.disableLockButton) {
        return
      }
      this.disableLockButton = true
      setDocumentState(this.file.id, 1, this.user.userId).then((res) => {
        this.$emit('updated')
        success('Document locked successfully')
        addComments('document', this.getLatestDocId.id, 'Document Locked', null)
      }).catch(() => {
        alert('Unable to lock document')
      }).finally(() => {
        this.disableLockButton = false
      })
    },
    unLock () {
      if (this.disableUnlockButton) {
        return
      }
      this.disableUnlockButton = true
      setDocumentState(this.file.id, 2, this.user.userId).then((res) => {
        this.$emit('updated')
        success('Document unlocked successfully')
        addComments('document', this.getLatestDocId.id, 'Document Unlocked', null)
      }).catch(() => {
        alert('Unable to unlock document')
      }).finally(() => {
        this.disableUnlockButton = false
      })
    },
    obselete () {
      if (this.disableObseleteButton) {
        return
      }
      this.disableObseleteButton = true
      setDocumentState(this.file.id, 4, this.user.userId).then((res) => {
        this.$emit('updated')
        success('Document obseleted successfully')
        addComments('document', this.getLatestDocId.id, 'Document Obseleted', null)
      }).catch(() => {
        alert('Unable to obselete document')
      }).finally(() => {
        this.disableObseleteButton = false
      })
    },
    nonObselete () {
      if (this.disableNonObseleteButton) {
        return
      }
      this.disableNonObseleteButton = true
      setDocumentState(this.file.id, 2, this.user.userId).then((res) => {
        this.$emit('updated')
        success('Document made non obsolete successfully')
        addComments('document', this.getLatestDocId.id, 'Document non obsolete', null)
      }).catch(() => {
        alert('Unable to make document non obsolete')
      }).finally(() => {
        this.disableNonObseleteButton = false
      })
    },
    checkin () {
      console.log('getLatestDocId', this.getLatestDocId)
      if (this.disableCheckinButton) {
        return
      }
      this.disableCheckinButton = true
      setDocumentState(this.file.id, 2, this.user.userId).then((res) => {
        this.$emit('updated')
        success('Document checked in successfully')
        addComments('document', this.getLatestDocId.id, 'Document checkedIn', null)
      }).finally(() => {
        this.disableCheckinButton = false
      })
    },
    checkout () {
      if (this.disableCheckoutButton) {
        return
      }
      this.disableCheckoutButton = true
      setDocumentState(this.file.id, 3, this.user.userId).then((res) => {
        this.$emit('updated')
        success('Document checked out successfully')
        addComments('document', this.getLatestDocId.id, 'Document checkedOut', null)
      }).finally(() => {
        this.disableCheckoutButton = false
      })
    },
    undoCheckout () {
      if (this.disableUndoCheckoutButton) {
        return
      }
      this.disableUndoCheckoutButton = true
      if (this.file.associated_versions.length > 0 && this.file.associated_versions.at(-1).state === META_CONFIG.DOC_STATE_MAP.CHECKOUT) {
        const latestRevisionId = this.file.associated_versions[this.file.associated_versions.length - 1].id
        deleteDocument(latestRevisionId).then(res => {
          this.checkin()
        }).catch(() => {
          alert('Unable to undo checkout')
        }).finally(() => {
          this.disableUndoCheckoutButton = false
        })
      } else {
        setDocumentState(this.file.id, 2, this.user.userId).then((res) => {
          this.$emit('updated')
          success('Document checked out back successfully')
        }).catch(() => {
          alert('Unable to undo checkout')
        }).finally(() => {
          this.disableUndoCheckoutButton = false
        })
      }
    },
    openDownLoadList () {
      if (this.file.inherited_from_doc_id) {
        this.downloadSelectedFile(this.file.inherited_from_document)
        return
      }
      if (this.fileVersions.length === 1) {
        this.downloadSelectedFile(this.fileVersions[0])
        return
      }
      this.openVersionList = true
    },
    downloadSelectedFile (version) {
      SendNotificationsForReadingAndDownloadingDocs(this.file.id, 'download')
      const docName = this.fileVersions[0].doc_name
      this.openVersionList = false
      this.isDownloading = true
      generateS3DownloadingUrlSingle({
        fileName: encodeURIComponent(docName),
        S3Key: version.blob_key
      }).then((resp) => {
        fetch(resp.url)
          .then((res) => {
            return res.blob()
          })
          .then((res) => {
            const blob = new Blob([res])
            const link = document.createElement('a')
            link.href = URL.createObjectURL(blob)
            link.download = docName
            link.click()
            URL.revokeObjectURL(link.href)
            this.isDownloading = false
          })
          .catch((err) => {
            this.isDownloading = false
            alert(err)
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.file-card {

  .grey-text {
    color: grey;
  }

  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  position: relative;
  &--image {
    width: 100%;
    height: 140px;
    background: #dbdbdb;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center;
    }
  }
  &-footer {
    .icon-btn {
      width: 30px;
      height: 30px;
      margin-right: 10px;
      border-radius: 50%;
      padding: 5px;
      cursor: pointer;
      &:hover {
        background: #e3e3e3;
      }
      &-document {
        cursor: pointer;
        padding-top: 7px;
        padding-right: 10px;
      }
    }
    img {
      width: 100%;
    }
  }
  &-action {
    &--btn {
      width: 20px;
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;
    }
    &:hover {
      .file-card-options {
        display: block;
      }
    }
  }
  &-options {
    position: absolute;
    top: 40px;
    right: 0;
    width: 140px;
    background-color: var(--white);
    border-radius: 5px;
    padding: 10px 2px;
    filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
    z-index: 1;
    display: none;
    &:before {
      content: "";
      position: absolute;
      top: -10px;
      right: 10px;
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-bottom: 10px solid var(--white);
    }
  }
  &-option {
    display: flex;
    align-items: center;
    padding: 5px 8px;
    cursor: pointer;
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
    img {
      width: 20px;
      margin-right: 8px;
    }
    span {
      font-size: 12px;
      font-weight: 400;
      line-height: 1;
      color: var(--text-color);
    }
  }
  [data-enable="true"] {
    opacity: 1;
    cursor: pointer;
    pointer-events: auto;
  }
  .chip {
    background: var(--brand-color);
    border-radius: 4px;
    padding: 5px 5px;
    font-size: 10px;
    margin-left: 6px;
    line-height: 1;
  }

  .obsolete {
    background: #ff0000;
    color: white;
  }

  .red {
    color: #ff0000;
  }
}
.file-name-card {
  background-color: var(--bg-color);
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 10px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  &:hover {
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  }
}
.download-doc-icon {
  position: relative;
  &-list {
    overflow: auto;
    position: absolute;
    background-color: var(--side-bar-color);
    opacity: 1;
    max-height: 200px;
    min-height: fit-content;
    width: 100px;
    right: 0;
    border-radius: 4px;
    list-style: none;
    border: 1px solid rgb(20, 8, 8, 0.2);
    bottom: 25px;
    & li {
      padding: 5px;
      border-bottom: 0.5px solid var(--brand-color);
    }
    & li:hover {
      background-color: var(--brand-color);
    }
    & li:last-child {
      border: none;
    }
  }
}
</style>
