<template>
     <div v-if="loadingList" style="height: 300px" class="center">
        <loading-circle />
      </div>
    <div v-else class="form-list fh">
      <div class="form-list-bar" >
        <div class="v-center space-between" >
          <div class="form-list-bar-title v-center">
            <label class="elipsis-text xxxl" v-overflow-tooltip>
              {{ templateName }}
            </label>
          </div>
        </div>
        <div class="space-between v-center">
          <div class="flex-start mt-3">
            <button class="btn btn-2 mr-2" v-if="isAdmin" @click="changeAssigneeModal = true">Change Assignee</button>
            <button class="btn btn-2 mr-2" @click="exportToCSV">Export CSV</button>
          </div>
          <div class="flex-end mt-3">
            <div>
              <label for="">Sort By</label>
              <select v-model="selectedSort" class="sort" name="" id="">
                <option value="1">Latest</option>
                <option value="2">Oldest</option>
              </select>
            </div>
            <div>
            </div>
            <button @click.stop="toggleDrawer" class="btn addfilter">
              <div v-if="getAppliedFilterNumber" class="addfilter-badge">{{ getAppliedFilterNumber}}</div>
              <div v-if="getAppliedFilterNumber" class="addfilter-ping"></div>
              Add Filter
            </button>
            <router-link :to="`/form/createform/${templateId}/${templateName}`">
          <button class="btn ml-2 pointer" v-if="showCreateForm">
            Create Form
          </button>
        </router-link>
            <!-- <img src="~@/assets/images/icons/more-icon.svg" width="20px" alt="" @click="openOptions" class="more-button"> -->
            <!-- <div v-if="showOptions" class="more-options">
            <div class="more-option">
              <span style="width: 100%; padding: 3px;" @click="changeAssigneeModal = true">Change Assignee</span>
            </div>
            <div class="more-option">
              <span style="width: 100%; padding: 3px;" @click="exportToCSV">Export CSV</span>
            </div>
            </div> -->
          </div>
        </div>
<div v-if="this.formList.length > 0" class="dashboard-container">
  <div class="total">
    <label for="" style="font-weight: 700; font-size: 24px;">{{ totalCount }}</label>
    <label >Total Forms</label>
  </div>

  <div class="status-card" v-for="(status, index) in statuses" :key="index">
    <img :src="status.image" alt="status-icon" class="status-image" />
<div class="status-value" style="display: flex; flex-direction: column; align-items: center;">
  <span style="font-weight: 400; font-size: 12px; margin-top: 4px;">{{ status.label }}</span>
  <span style="font-weight: 700; font-size: 18px;">{{ status.value }}</span>
</div>
  </div>
</div>

        <div class="drawer" :class="drawer ? 'open' : 'close'" ref="drawer">
          <div clas="filters">
            <div class="input-group m">
              <h5 class="filter-by">Filter by ID :</h5>
              <input type="text" :class="{
                selectedOne: searchById,
              }" v-model="filtersTemp.searchById" placeholder=" Filter by ID" />
            </div>
            <div class="input-group m">
              <h5 class="filter-by mt-5">Filter By Assignee :</h5>
              <div class="" ref="addUserBody">
                <input placeholder="Search for users" type="text" v-model="filtersTemp.searchKeyWord" @click.stop="filtersTemp.openPopup = true" />
                <div class="form-list__selected-user mt-4 mb-4">
                  <div class="assignees-box" v-for="(user, index) in filtersTemp.selectedAssignees" :key="user.id">
                    <div class="form-list__selected-user__avatar mr-2">
                      <avatar :user="user" size="24px" />
                    </div>
                    <div class="form-list__selected-user__name">
                      {{ user.first_name }} {{ user.last_name }}
                    </div>
                    <div class="form-list__selected-user__action">
                      <b @click.stop="deselectUser(index, true)">X</b>
                    </div>
                  </div>
                </div>

                <div class="form-list__list" v-if="filtersTemp.openPopup">
                  <div class="add-user__list-no-result" v-if="!users.length">
                    No users found
                  </div>
                  <div class="form-list__list-item" v-for="user in users" :key="user.id" :value="user.id"
                    @click.stop="selectUser(user, true)">
                    <div class="form-list__list-item__avatar mr-2">
                      <avatar :user="user" size="24px" />
                    </div>
                    <div class="form-list__list-item__name">
                      {{ user.first_name }}
                      {{ user.last_name }}
                    </div>
                    <div class="form-list__list-item__action"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="input-group m">
              <h5 class="filter-by">Filter By Status :</h5>
              <select v-model="filtersTemp.statusToFilter">
                <option value="-1" disabled>Filter by Status</option>
                <option value="1">OPEN</option>
                <option value="2">DRAFT</option>
                <option value="3">REOPENED</option>
                <option value="4">CLOSED</option>
              </select>
            </div>
            <h4 class="key mt-5">Created On :</h4>
            <div class="grid-2 mt-2">
              <div class="input-group mt-2">
                <h5 class="filter-by">From :</h5>
                <input type="date" placeholder="Created On" v-model="filtersTemp.createdDate.from"
                  :class="{ selectedOne: filtersTemp?.createdDate?.from }" />
              </div>
              <div class="input-group mt-2">
                <h5 class="filter-by">To :</h5>
                <input type="date" placeholder="" v-model="filtersTemp.createdDate.to" :min="filtersTemp.createdDate.from"
                  :class="{ selectedOne: filtersTemp.createdDate.to }" />
              </div>
            </div>
            <div>
              <h4 class="key mt-5">Due Date :</h4>
              <div class="grid-2 mt-2">
                <div class="input-group mt-2">
                  <h5 class="filter-by">From :</h5>
                  <input type="date" placeholder="Due Date" v-model="filtersTemp.dueDate.from"
                    :class="{ selectedOne: filtersTemp.dueDate?.from }" />
                </div>
                <div class="input-group mt-2">
                  <h5 class="filter-by">To :</h5>
                  <input type="date" placeholder="" v-model="filtersTemp.dueDate.to" :min="filtersTemp.dueDate.from"
                    :class="{ selectedOne: filtersTemp.dueDate.to }" />
                </div>
              </div>
            </div>
            <div>
              <h4 class="key mt-5">Updated On :</h4>
              <div class="grid-2 mt-2">
                <div class="input-group mt-2">
                  <h5 class="filter-by">From :</h5>
                  <input type="date" placeholder="Due Date" v-model="filtersTemp.updatedDate.from"
                    :class="{ selectedOne: filtersTemp.updatedDate?.from }" />
                </div>
                <div class="input-group mt-2">
                  <h5 class="filter-by">To :</h5>
                  <input type="date" placeholder="" v-model="filtersTemp.updatedDate.to" :min="filtersTemp.updatedDate.from"
                    :class="{ selectedOne: filtersTemp.updatedDate.to }" />
                </div>
              </div>
            </div>
            <div class="mt-3">
              <span
                :class="{ disabled:!formFields.length || filtersTemp.customFilters.length }"
                class="v-center mt-2 m underline pointer gap-1"
                @click.stop="checkAndAddCustomFilter"
                >View more filters
                <img
                  src="~@/assets/images/icons/arrow-down-icon.svg"
                  height="15px"
                  alt=""/>
                </span>
            </div>
            <div class="flex" v-for="(customFilter, index) in filtersTemp.customFilters" :key="customFilter.field">
              <div class="grid-2 mt-2 custom-filters">
                <div class="input-group mt-2">
                  <select v-model="filtersTemp.customFilters[index].field" @change="handleCustomFilterChange(index)">
                    <option v-for="field in formFields" :key="field.field_id" :value="field.field_id"
                      :disabled="checkIfFieldIsAlreadyThere(field.field_id)">
                      {{ field.caption }}
                    </option>
                  </select>
                </div>
                <div v-click-outside="handleClickOutside" class="input-group mt-2" v-if="formFieldsTypeMap[filtersTemp.customFilters[index].field] === 4">
        <div class="input-group">
        <input type="text" @click="toggleDropdown(index, $event)" :value="displaySelectedStatus(index)" placeholder="Select the Dropdown.." readonly />
      </div>
      <div v-if="openDropdownIndex === index">
        <div  class="configList" @click.stop>
          <div class="flex my-1">
          <input style="width: 12px" type="checkbox" :checked="isAllSelected(index)"
          @change="toggleSelectAll(index)" />
          <h4 class="ml-3">Select All</h4>
        </div>
          <div class="flex mt-1" v-for="option in formFieldsConfigurationListMap[
                      filtersTemp.customFilters[index].field
                    ]" :key="option.id" :value="option.id" @click="toggleCustomStatus(index, option)">
            <input style="width: 12px" type="checkbox" :checked="isChecked(index, option.id)"/>
            <div class="ml-3">
                {{ option.name }}
            </div>
          </div>
        </div>
      </div>
      </div>
                <!-- <div class="input-group mt-2" v-if="formFieldsTypeMap[filtersTemp.customFilters[index].field] === 4">
                  <select v-model="filtersTemp.customFilters[index].value">
                    <option v-for="option in formFieldsConfigurationListMap[
                      filtersTemp.customFilters[index].field
                    ]" :key="option.id" :value="option.id">
                      {{ option.name }}
                    </option>
                  </select>
                </div> -->
                <div class="input-group mt-2" v-else>
                  <input v-model="filtersTemp.customFilters[index].value">
                </div>
              </div>
              <button class="btn ml-2 mt-4" @click.stop="removeCustomFilter(index)">
                <img src="~@/assets/images/icons/close-icon.svg" width="20px" alt="dd" />
              </button>
            </div>
            <div v-if="filtersTemp.customFilters.length">
              <span
                :class="{ disabled: filtersTemp.customFilters.length === formFields.length }"
                class="underline mt-2 m pointer"
                style="display: inline-flex; align-items: center;"
                @click.stop="addCustomFilter"
                >
                Add
                <img
                  class="ml-1"
                  src="~@/assets/images/icons/add-new-icon.svg"
                  height="15px"
                  alt=""/>
                </span>
            </div>
          </div>
          <div class="v-center space-between mb-5">
            <button class="btn mt-5 btn-black" @click.stop="cancelFilter">
              Cancel
            </button>
            <button class="btn mt-5" @click.stop="clearAllData">Clear All</button>
            <button class="btn mt-5" @click.stop="applyFilters">Apply</button>
          </div>
        </div>
      </div>
      <div v-if="formList.length === 0" style="height: 300px" class="center">
        No Forms Found for this Template.
      </div>
      <div class="form-list-container" v-else>
        <div class="copy-dtx-table form-table">
          <table>
            <thead>
              <tr>
                <th v-overflow-tooltip v-if="formList[0].template_version?.core_form_template?.sequence_template_id">Sequence Id</th>
                <th v-else>S.No</th>
                <th v-overflow-tooltip>{{ value[0]?.value }}</th>
                <th v-overflow-tooltip>{{ value[1]?.value }}</th>
                <th v-overflow-tooltip>Status</th>
                <th v-overflow-tooltip>Due Date</th>
                <th v-overflow-tooltip>Created At</th>
                <th v-overflow-tooltip>Created By</th>
                <th v-overflow-tooltip>Assigned Users</th>
                <th v-overflow-tooltip>Updated At</th>
                <th v-overflow-tooltip>Updated By</th>
                <th v-if="isOnProjectLevel ? isProjectAdmin : isTenantAdmin" v-overflow-tooltip>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in formList" :key="row.id"
              @click="
                        $router.push(
                          `/form/viewform/${templateId}/${templateName}/${row.id}`
                        )"
              >
                <template v-if="
                  row.status !== FORM_STATE_MAP.DRAFT || user.userId === row.created_by || isAdmin
                ">
                  <td v-overflow-tooltip v-if="formList[0].template_version?.core_form_template?.sequence_template_id">
                    {{row.sequence_value ?? '--'}}
                  </td>
                  <td v-else>{{ (pageNumber - 1) * perPage + index + 1 + ")" }}</td>
                  <td v-overflow-tooltip v-if="row?.customVisibleField1?.tooltip?.length">
                    <span v-tooltip="row?.customVisibleField1?.tooltip">{{
                      row?.customVisibleField1?.value
                    }}</span>
                  </td>
                  <td v-overflow-tooltip v-else>
                    {{ row?.customVisibleField1?.value }}
                  </td>
                  <td v-overflow-tooltip v-if="row?.customVisibleField2?.tooltip?.length">
                    <span v-tooltip="row?.customVisibleField2?.tooltip">
                      {{ row?.customVisibleField2?.value }}
                    </span>
                  </td>
                  <td v-overflow-tooltip v-else>
                    {{ row?.customVisibleField2?.value }}
                  </td>
                  <td>
                    <div class="status-display">
                    <img
                    :src="getStatusImage(row.status)"
                    alt="status-icon"
                   class="status-icon"
                    />
                  {{ formStateMap[row.status] }}
                  </div>
                  </td>
                  <td v-overflow-tooltip>{{ row.due_date | genericFormatDate }}</td>
                  <td v-overflow-tooltip>{{ row.created_on | genericFormatDate }}</td>
                  <td v-overflow-tooltip>
                    {{
                      row.created_by
                        ? row.created_by_user?.first_name +
                        " " +
                        row.created_by_user?.last_name
                        : "--"
                    }}
                  </td>
                  <td>
                    <div class="avatar-container">
                      <avatar v-for="user in row.forms_user_lists" :key="user.id" :user="user.core_user"
                        :title="getUserTooltip(user.core_user)" style="margin-right: -13.5px" />
                    </div>
                  </td>
                  <td v-overflow-tooltip>{{ row.updated_on | genericFormatDate }}</td>
                  <td v-overflow-tooltip>
                    {{
                      row.updated_by
                        ? row.updated_by_user?.first_name +
                        " " +
                        row.updated_by_user?.last_name
                        : "--"
                    }}
                  </td>
                  <td v-if="isOnProjectLevel ? isProjectAdmin : isTenantAdmin" class="action-column">
                    <div class="action-icons">
                        <img src="~@/assets/images/trash-2.svg"
                        :class="['delete pointer',{ disabled: isDeleteDisabled(row.status) }]"
                        @click.stop="deleteForm(index)"
                        alt=""
                        v-tooltip="'Delete form'"/>
                        <img class="edit" v-if="
                        row.status === FORM_STATE_MAP.CLOSED &&
                        (user.userId === row.created_by || isAdmin) &&
                        !collaborator && (row.form_type_id !== config.DOCUMENT_REVISIONING_FORM.form_type)
                      " src="~@/assets/images/icons/undo-icon.svg" v-tooltip="'Reopen Form'"
                        @click.stop="reopenForm(index, row)" />
                    </div>
                  </td>
                </template>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination-footer">
            <pagination2 v-if="totalCount > perPage" class="mt-4" :length="totalCount" :pageNumber="pageNumber" :perPage="perPage"
              @selectPage="selectPage" />
            <span class="pagination-footer-total mt-4">Total Forms : &nbsp; <b> {{ totalCount }}</b></span>
          </div>
      </div>
      <modal :open="changeAssigneeModal" @close="[changeAssigneeModal = false,resetChangeAssigneeModal()]" :closeOnOutsideClick="true"
        title="Change Form Assignee">
        <div class="p-3">
          <div class="grid-2 mt-2">
            <div class="input-group ">
              <label class="key">Template Name:</label>
              <input type="text" :value="templateName" disabled />
            </div>
            <div class="input-group imp">
              <label class="key">Status</label>
              <select v-model="status">
                <option value="1">OPEN</option>
                <option value="2">DRAFT</option>
                <option value="3">REOPENED</option>
              </select>
            </div>
          </div>
          <div class=" mt-2">
            <div class="input-group imp">
              <label class="key">Current Assignee</label>
             <select :disabled="!status" v-model="existingUserId">
              <option v-if="!getChangeAssigneeList.length">No users found</option>
              <option v-for="user in getChangeAssigneeList" :key="user.id" :value="user.id">{{ user.first_name + ' '+ user.last_name }}</option>
            </select>
            </div>
          </div>
          <div class="grid-2 mt-2">
            <div class="input-group imp">
              <label class="key">Company:</label>
              <select :disabled ="!existingUserId" v-model="companyValue" @change="companySelected">
                <option v-for="item in tenantList" :key="item.id" :value="item.id">
                      {{ item.company_name }}
                    </option>
                    <optgroup label="Child Tenants" v-if="childTenantsList.length">
                      <option v-for="childTenant in filterChildTenant" :key="childTenant.id"
                        :value="childTenant.target_tenant.id"
                        :disabled="childTenant.status === 2 || childTenant.status === 3">
                        {{ childTenant.target_tenant.company_name }}
                      </option>
                    </optgroup>
              </select>
            </div>
            <div class="input-group imp mt-3">
              <label class="key">New Assignee</label>
              <div :class="{
                'form-input': true,
              }" @mouseleave="handlemousehover">
                <div v-if="selectedAssignee.id" class="form-user-bedge">
                  <div>
                    <div class="flex">
                      <div class="form-user-bedge__name">
                        {{ selectedAssignee.first_name }} {{ selectedAssignee.last_name }}
                      </div>
                      &nbsp; {{ selectedAssignee?.status === 4 ? "(Invitation not accepted yet)" : null }}
                    </div>

                    <div class="form-user-bedge__email">{{ selectedAssignee.email }}</div>
                  </div>

                  <div v-if="selectedAssignee.id" class="form-user-bedge__action" @click="removeUser">
                    <img src="~@/assets/images/delete-gray-icon.svg" width="20px" />
                  </div>
                </div>
                <input v-else ref="assigneeInput" type="text" class="form-user-search" v-model="userSearchKeyword" @focus="open = true" />
                <div class="form-input--options" v-if="open && !selectedAssignee.id" @mouseleave="open=false">
                  <div v-if="getUserList.length === 0" class="form-input--option">
                    <div class="form-input--option__name">No users found</div>
                  </div>
                  <div class="form-input--option" v-for="user in getUserList" :key="user.id" @click="selectUser(user)">
                    <div class="form-input--option__name">
                      {{ user.first_name }} {{ user.last_name }}
                    </div>
                    <div class="form-input--option__email">{{ user.email }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-end pt-3 mt-2">
            <button class="btn btn-black mr-3 pointer" @click="resetChangeAssigneeModal">
              CANCEL
            </button>
            <button class="btn pointer" :disabled="changeAssigneeButtonDisabled ||  !this.existingUserId || !this.selectedAssignee.id || !this.companyValue " @click="reAssignUser">
              SAVE
            </button>
          </div>
        </div>
      </modal>
    </div>
  </template>
<script>
import {
  GetFormsByFormTemplateId,
  // EditForm,
  DeleteForm,
  getFormCustomFeilds,
  GetAllAssigneesOfATemplate,
  GetAllFormsDataByFormTemplateId,
  GetAllformFieldsOfAllVersionsByTemplateId,
  GetFieldsForCustomFormFilters,
  getTenantUsersListwithInvitedUsers,
  reAssignToNewUser,
  GetUserListByPojIds
} from '@/api'
import { timeStampToDate } from '@/filters/dateFilter'
import { success, alert } from '@/plugins/notification'
import { mapGetters } from 'vuex'
import loadingCircle from '../../components/common/loadingCircle.vue'
import avatar from '../../components/common/avatar.vue'
import pagination2 from '../../components/common/pagination2.vue'
import Config from '@/config'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import { arrayToCsv } from '@/helper/file/arrayToCsv'
import { formatFullDate, genericFormatDate, Duration } from '@/utils/date'
import Loader from '@/plugins/loader'
import { previousPath } from '@/router'
import setVisibleFieldValues from '@/helper/insights/setVisibleFieldValues'
import Modal from '../../components/common/modal.vue'

export default {
  components: { loadingCircle, avatar, pagination2, Modal },
  name: 'FormListComponent',
  filters: {
    timeStampToDate: timeStampToDate, genericFormatDate, Duration
  },
  data () {
    return {
      showOptions: false,
      statuses: [
        {
          id: 1,
          label: 'Open Forms',
          value: 0,
          image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%235ba3e5' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round' class='lucide lucide-lock-open-icon lucide-lock-open'%3E%3Crect width='18' height='11' x='3' y='11' rx='2' ry='2'/%3E%3Cpath d='M7 11V7a5 5 0 0 1 9.9-1'/%3E%3C/svg%3E"
        },
        {
          id: 2,
          label: 'Draft Forms',
          value: 0,
          image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%239d56bd' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2a10 10 0 0 1 7.38 16.75'/%3E%3Cpath d='M12 6v6l4 2'/%3E%3Cpath d='M2.5 8.875a10 10 0 0 0-.5 3'/%3E%3Cpath d='M2.83 16a10 10 0 0 0 2.43 3.4'/%3E%3Cpath d='M4.636 5.235a10 10 0 0 1 .891-.857'/%3E%3Cpath d='M8.644 21.42a10 10 0 0 0 7.631-.38'/%3E%3C/svg%3E"
        },
        {
          id: 3,
          label: 'Reopen Forms',
          value: 0,
          image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23d9b568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m12 15 2 2 4-4'/%3E%3Crect width='14' height='14' x='8' y='8' rx='2' ry='2'/%3E%3Cpath d='M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2'/%3E%3C/svg%3E"
        },
        {
          id: 4,
          label: 'Closed Forms',
          value: 0,
          image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='28' height='28' viewBox='0 0 24 24' fill='none' stroke='%2343d68a' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpath d='m9 12 2 2 4-4'/%3E%3C/svg%3E"
        }
      ],

      selectedSort: '1',
      formFieldsConfigurationListMap: {},
      formFieldsTypeMap: {},
      formFields: [],
      value: [],
      customFilters: [],
      customFiltersMap: {},
      extractedValues: {},
      formDetails: {},
      fields: [],
      field2: null,
      fieldValues: [],
      templateVisibleFieldsMap: {},
      formStateMap: Config.FORM_STATE,
      loadingList: false,
      formList: [],
      templateId: '',
      templateName: '',
      drawer: false,
      userList: [],
      searchKeyWord: '',
      statusToFilter: -1,
      openPopup: false,
      selectedUserId: null,
      FORM_STATE_MAP: Config.FORM_STATE_MAP,
      searchById: '',
      dueDate: {
        from: null,
        to: null
      },
      createdDate: {
        from: null,
        to: null
      },
      updatedDate: {
        from: null,
        to: null
      },
      selectedAssignees: [],
      totalCount: 0,
      pageNumber: 1,
      perPage: 12,
      changeAssigneeModal: false,
      data: {},
      open: false,
      companyValue: null,
      selectedAssignee: {},
      existingUserId: null,
      userSearchKeyword: '',
      tenantUsers: [],
      changeAssigneeButtonDisabled: false,
      status: null,
      openDropdownIndex: null,
      config: Config,
      filtersTemp: {
        searchById: '',
        searchKeyWord: '',
        statusToFilter: -1,
        createdDate: { from: null, to: null },
        updatedDate: { from: null, to: null },
        dueDate: { from: null, to: null },
        selectedAssignees: [],
        selectedAssignee: {},
        customFilters: [],
        customFiltersMap: {},
        openPopup: false
      }
    }
  },
  watch: {
    selectedSort (value) {
      if (value === '0') return
      console.log('hitting')
      this.setup()
    },
    $route () {
      this.clearAllData({ clearUserAndFieldsCache: true })
    },
    changeAssigneeModal () {
      this.open = false
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel', 'isTenantAdmin', 'isProjectAdmin', 'user', 'collaborator', 'currentProject', 'openTenantId', 'openProjectId']),
    ...mapGetters(['childTenantsList', 'isOnProjectLevel', 'tenantList']),
    ...mapGetters('form', [
      'formTypeList',
      'customFiltersFromStore',
      'formFiltersFromStore',
      'formFieldsTypeMapStore'
    ]),
    isDeleteDisabled () {
      return (status) => {
        return status === this.FORM_STATE_MAP.CLOSED || status === this.FORM_STATE_MAP.REOPENED
      }
    },
    filterChildTenant () {
      return this.childTenantsList.filter((item) => item.target_tenant.status === 1)
    },
    showCreateForm () {
      if (this.collaborator) {
        return false
      }
      if (this.isOnProjectLevel) {
        return this.user.projectLevelRole === 'ADMIN' || this.user.projectLevelRole === 'COLLABORATOR'
      } else {
        return this.user.tenantLevelRole === 'ADMIN' || this.user.tenantLevelRole === 'COLLABORATOR'
      }
    },
    isAdmin () {
      // tenantLevelRole is giving independent tenant level role
      return (this.user.tenantLevelRole === 'ADMIN' || this.user.projectLevelRole === 'ADMIN') && !this.collaborator
    },
    getChangeAssigneeList () {
      const assignees = this.formList.flatMap(item =>
        item.forms_user_lists.filter(user => item.status === parseInt(this.status)).map(user => user.core_user)
      )
      const uniqueAssignees = assignees.filter((value, index, self) =>
        index === self.findIndex((t) => (
          t.id === value.id
        ))
      )

      return uniqueAssignees
    },
    users () {
      if (!Array.isArray(this.userList)) {
        return []
      }
      let targetArr = this.selectedAssignees
      if (!this.changeAssigneeModal) {
        targetArr = this.filtersTemp.selectedAssignees
      }
      const usersList = []
      const searchKeyWord = this.filtersTemp.searchKeyWord.toLowerCase()
      this.userList.forEach((user) => {
        if (!(user && !targetArr.find((item) => item?.id === user.id))) {
          return
        }
        if (searchKeyWord !== '' && !(user.first_name.toLowerCase().includes(searchKeyWord) || user.last_name.toLowerCase().includes(searchKeyWord))) {
          return
        }
        usersList.push({
          id: user.id,
          first_name: user?.first_name,
          last_name: user?.last_name
        })
      })

      const uniqueUsersList = Array.from(
        new Map(usersList.map((item) => [item.id, item])).values()
      )
      return uniqueUsersList
    },
    getAppliedFilterNumber () {
      let filterNo = 0
      if (this.searchById) {
        filterNo++
      }
      if (this.selectedAssignees.length > 0) {
        filterNo++
      }
      if (this.customFilters.length > 0) {
        filterNo += this.customFilters.length
      }
      if (parseInt(this.statusToFilter) !== -1) {
        filterNo++
      }
      if (this.createdDate?.from || this.createdDate?.to) {
        filterNo++
      }
      if (this.updatedDate?.from || this.updatedDate?.to) {
        filterNo++
      }
      if (this.dueDate?.from || this.dueDate?.to) {
        filterNo++
      }
      if (!filterNo) {
        return false
      }
      return String(filterNo)?.padStart(2, '0')
    },
    getUserList () {
      if (!Array.isArray(this.tenantUsers) || !this.companyValue) {
        return []
      }
      let usersList
      if (this.userSearchKeyword) {
        usersList = this.tenantUsers.map((item) => {
          return {
            ...item.associated_user
          }
        }).filter((item) => `${item.first_name} ${item.last_name}`.replace(' ', '').toLowerCase().includes(this.userSearchKeyword.toLowerCase().replace(' ', '')))
      } else {
        usersList = this.tenantUsers.map((item) => {
          return {
            ...item.associated_user
          }
        })
      }
      return usersList
    }
  },
  methods: {
    getStatusImage (statusId) {
      const match = this.statuses.find(s => s.id === statusId)
      return match ? match.image : ''
    },
    displaySelectedStatus (index) {
      const field = this.filtersTemp.customFilters[index].field
      const selectedIds = this.filtersTemp.customFilters[index].value || []
      const options = this.formFieldsConfigurationListMap[field] || []
      const selectedNames = options
        .filter(option => selectedIds.includes(option.id))
        .map(option => option.name)
      if (selectedNames.length > 3) {
        const names = selectedNames.slice(0, 3).join(', ')
        const remaining = selectedNames.length - 3
        return `${names} +${remaining}`
      } else {
        return selectedNames.length > 0 ? selectedNames.join(', ') : 'Select the Dropdown..'
      }
    },
    openOptions () {
      if (this.showOptions === true) {
        this.showOptions = false
      } else {
        this.showOptions = true
      }
      setTimeout(() => {
        document.addEventListener('click', this.clickOutside)
      }, 500)
    },
    toggleDropdown (index, event) {
      event.stopPropagation()
      this.openDropdownIndex = this.openDropdownIndex === index ? null : index
    },
    handleClickOutside () {
      this.openDropdownIndex = null // Close the dropdown
    },
    isAllSelected (index) {
      const selected = this.filtersTemp.customFilters[index].value || []
      const allOptions = this.formFieldsConfigurationListMap[this.filtersTemp.customFilters[index].field] || []
      return selected.length === allOptions.length
    },

    toggleSelectAll (index) {
      const allOptions = this.formFieldsConfigurationListMap[this.filtersTemp.customFilters[index].field] || []
      const allIds = allOptions.map(opt => opt.id)

      const current = this.filtersTemp.customFilters[index]
      const selected = current.value || []

      if (selected.length === allOptions.length) {
        this.filtersTemp.customFilters[index].value = []
      } else {
        this.filtersTemp.customFilters[index].value = [...allIds]
      }
    },
    isChecked (index, id) {
      const selected = this.filtersTemp.customFilters[index].value || []
      return selected.includes(id)
    },
    toggleCustomStatus (index, status) {
      const valueArray = this.filtersTemp.customFilters[index].value || []
      const i = valueArray.indexOf(status.id)
      if (i === -1) {
        valueArray.push(status.id)
      } else {
        valueArray.splice(i, 1)
      }
      this.filtersTemp.customFilters[index].value = valueArray
    },
    handleCustomFilterChange (index) {
      // if (
      //   this.formFieldsConfigurationListMap[this.filtersTemp.customFilters[index].field]
      // ) {
      //   this.filtersTemp.customFilters[index].value =
      //     this.formFieldsConfigurationListMap[
      //       this.filtersTemp.customFilters[index].field
      //     ][0].id
      // } else {
      this.filtersTemp.customFilters[index].value = ''
      // }
    },
    checkIfFieldIsAlreadyThere (fieldId) {
      const alreadyThere = this.filtersTemp.customFilters.find(
        (customFilter) => customFilter.field === fieldId
      )
      this.filtersTemp.customFiltersMap[fieldId] = !alreadyThere
      return alreadyThere
    },
    addCustomFilter () {
      let nextAutoSelectedField = ''
      for (const field in this.filtersTemp.customFiltersMap) {
        const existingField = this.filtersTemp.customFilters.find(filter => {
          return filter.field === field
        })
        if (this.filtersTemp.customFiltersMap[field] && !existingField) {
          nextAutoSelectedField = field
          break
        }
      }
      if (nextAutoSelectedField === '') return
      const customFilter = { value: '', field: nextAutoSelectedField }
      this.filtersTemp.customFilters.push(customFilter)
    },
    checkAndAddCustomFilter () {
      if (!this.filtersTemp.customFilters.length) {
        this.addCustomFilter()
      }
    },
    removeCustomFilter (index) {
      this.filtersTemp.customFilters.splice(index, 1)
    },
    resetPagination () {
      this.pageNumber = 1
    },
    isAssignee (assignees) {
      for (const assignee of assignees) {
        if (assignee.user_id === this.user.userId) {
          return true
        }
      }
    },
    reopenForm (index, form) {
      this.$router.push(`/form/createform/${this.templateId}/${this.templateName}/${form.id}`)
    },
    deleteForm (index) {
      const id = this.formList[index].id
      ConfirmationDialog('Are you sure you want to delete ?', (res) => {
        if (res) {
          DeleteForm(id, this.isOnProjectLevel)
            .then((res) => {
              this.formList.splice(index, 1)
              if (this.formList.length === 0 && this.pageNumber > 1) {
                this.pageNumber = this.pageNumber - 1
              }
              this.setup()
              success('Successfully deleted form')
            })
            .catch(() => {
              alert('Failed to delete form')
            })
        }
      })
    },
    getFormType (formTypeId) {
      const formType = this.formTypeList.find(
        (formType) => formType.id === formTypeId
      )
      return formType ? formType.name : ''
    },
    async parseSelectedCustomFormFields () {
      const res = await getFormCustomFeilds(this.templateId, undefined, 'forms', this.collaborator)
      this.fields = []
      this.value = []
      this.templateVisibleFieldsMap = {}
      this.templateVisibleFieldsMap[this.templateId] = []
      this.fieldValues = res.template_fields.map((values) => {
        this.templateVisibleFieldsMap[this.templateId].push({
          id: values.field_id,
          key: values.form_field.key,
          caption: values.caption
        })
        if (!(!this.isOnProjectLevel && values.caption === 'Bom')) {
          this.value.push({
            value: values.caption,
            field_id: values.field_id
          })
        }
        this.fields.push(values.form_field.caption)
        return values.field_id
      })
    },
    async setup () {
      try {
        this.loadingList = true
        const assigneeId = this.selectedAssignees.map((item) => item.id)
        const filter = {
          jump: (this.pageNumber - 1) * this.perPage,
          perPage: this.perPage,
          searchById: this.searchById ? this.searchById : undefined,
          dueDate: this.dueDate,
          createdOnDate: this.createdDate,
          updatedOnDate: this.updatedDate,
          assigneeId: this.selectedAssignees.length ? assigneeId : undefined,
          status: parseInt(this.statusToFilter),
          pageNumber: this.pageNumber,
          selectedAssignees: this.selectedAssignees
        }
        this.$store.dispatch('form/saveFormFilters', {
          filters: filter,
          customFilters: this.customFilters
        })
        this.closeDrawer()
        if (!this.templateVisibleFieldsMap[this.templateId]) { await this.parseSelectedCustomFormFields() }
        GetAllAssigneesOfATemplate(this.templateId, this.isOnProjectLevel, this.currentProject?.id).then(assigneesRes => {
          this.userList = []
          assigneesRes.forms_user_list.map((item) => {
            this.userList.push(item.core_user)
          })
        })

        GetFormsByFormTemplateId(
          this.templateId,
          this.isOnProjectLevel,
          filter,
          this.isAdmin,
          this.user.userId,
          this.fieldValues,
          this.customFilters,
          this.formFieldsTypeMap,
          this.selectedSort
        )
          .then((res) => {
            this.formList = []
            const statusCountMap = {
              1: res.open_forms?.aggregate?.count || 0, // Open Forms
              2: res.draft_forms?.aggregate?.count || 0, // Draft Forms
              3: res.reopened_forms?.aggregate?.count || 0, // Reopen Forms
              4: res.closed_forms?.aggregate?.count || 0 // Closed Forms
            }

            this.statuses = this.statuses.map(status => ({
              ...status,
              value: statusCountMap[status.id] ?? status.value
            }))
            res.core_forms.forEach((item) => {
              if (
                item.status !== 2 ||
                    ((this.user.userId === item.created_by || this.isAdmin) &&
                      !this.collaborator)
              ) {
                if (this.templateVisibleFieldsMap[this.templateId].length) {
                  setVisibleFieldValues(
                    item,
                    this.templateId,
                    this.templateVisibleFieldsMap,
                    this.isOnProjectLevel
                  )
                }
                console.log('')
                this.formList.push(item)
              }
            })
            this.totalCount = res.core_forms_aggregate.aggregate.count
          })
          .catch((err) => {
            console.log(err)
            alert('Something went wrong')
          })
          .finally(() => {
            this.loadingList = false
          })
      } catch (err) {
        this.loadingList = false
        alert(err?.message ?? 'Unable to fetch data')
      }
    },
    toggleDrawer (event) {
      if (!this.drawer) {
        GetFieldsForCustomFormFilters(this.templateId, this.isOnProjectLevel)
          .then((res) => {
            this.formFields = res.template_fields
            this.formFields.forEach((field) => {
              this.formFieldsTypeMap[field.field_id] = field.field_type_id
              if (field.field_type_id === 4) {
                this.formFieldsConfigurationListMap[field.field_id] =
                    field.core_custom_list.custom_list_values
              }
              this.customFiltersMap[field.field_id] = true
            })
            // saves the formFieldType while navigating
            this.$store.dispatch('form/saveFormFieldsTypeMap', this.formFieldsTypeMap)
            this.resetTempFilters()
          })
          .catch(() => {
            alert('Unable to fetch data')
          })
        this.openDrawer()
      } else {
        this.closeDrawer()
      }
    },
    openDrawer () {
      this.drawer = true
      this.$nextTick(() => {
        this.$refs.drawer.focus()
      })
    },
    closeDrawer () {
      this.drawer = false
      this.resetTempFilters()
    },
    selectUser (user, temp = false) {
      if (!temp) {
        this.selectedAssignees.push(user)
        this.selectedAssignee = user
        this.openPopup = false
      } else {
        this.filtersTemp.selectedAssignees.push(user)
        this.filtersTemp.selectedAssignee = user
        this.filtersTemp.openPopup = false
      }
    },
    deselectUser (index, temp = false) {
      if (!temp) {
        this.selectedAssignees.splice(index, 1)
      } else {
        this.filtersTemp.selectedAssignees.splice(index, 1)
      }
    },
    resetTempFilters () {
      this.filtersTemp = JSON.parse(JSON.stringify({
        searchById: this.searchById,
        searchKeyWord: this.searchKeyWord,
        statusToFilter: this.statusToFilter,
        createdDate: this.createdDate,
        updatedDate: this.updatedDate,
        dueDate: this.dueDate,
        selectedAssignees: this.selectedAssignees,
        selectedAssignee: this.selectedAssignee,
        customFilters: this.customFilters,
        customFiltersMap: this.customFiltersMap,
        openPopup: false
      }))
    },
    cancelFilter () {
      this.closeDrawer()
      this.resetTempFilters()
    },
    handleOutsideClick (e) {
      if (this.openPopup && !this?.$refs?.addUserBody?.contains(e.target)) {
        this.openPopup = false
      } else if (this.drawer && !this?.$refs?.drawer?.contains(e.target)) {
        this.closeDrawer()
      }
    },
    clearAllData ({ clearUserAndFieldsCache = false }) {
      this.searchById = ''
      this.dueDate = {}
      this.createdDate = {}
      this.updatedDate = {}
      this.selectedAssignees = []
      this.statusToFilter = -1
      this.customFilters = []
      this.resetTempFilters()
      if (clearUserAndFieldsCache) {
        this.userList = []
        this.templateVisibleFieldsMap = {}
      }

      this.resetPagination()
      this.setup()
    },
    applyFilters () {
      const filtersTemp = JSON.parse(JSON.stringify(this.filtersTemp))
      this.searchById = filtersTemp.searchById
      this.dueDate = filtersTemp.dueDate
      this.createdDate = filtersTemp.createdDate
      this.updatedDate = filtersTemp.updatedDate
      this.selectedAssignees = filtersTemp.selectedAssignees
      this.statusToFilter = filtersTemp.statusToFilter
      this.customFilters = filtersTemp.customFilters
      this.customFiltersMap = filtersTemp.customFiltersMap
      this.resetPagination()
      this.setup()
      this.handleClickOutside()
    },
    getUserTooltip (user) {
      // You can customize the tooltip content here
      return `${user?.first_name} ${user?.last_name}` // Assuming `name` is a property of the user object
    },
    selectPage (pageNumber) {
      this.pageNumber = pageNumber
      this.setup()
    },
    exportToCSV () {
      const self = this
      const loader = new Loader()
      loader.show()
      const assigneeId = this.selectedAssignees.map((item) => item.id)
      const filter = {
        jump: 0,
        perPage: 10000,
        // jump: (this.pageNumber - 1) * this.perPage,
        // perPage: this.perPage,
        searchById: this.searchById ? this.searchById : undefined,
        dueDate: this.dueDate,
        createdOnDate: this.createdDate,
        assigneeId: this.selectedAssignees.length ? assigneeId : undefined,
        status: parseInt(this.statusToFilter)
      }
      if (this.totalCount > 10000) {
        ConfirmationDialog(
          'You can export only  first 10000 forms! ',
          function (res) {
            if (res) {
              exportcsvfunction()
            }
          }
        )
      } else {
        exportcsvfunction()
      }

      function exportcsvfunction () {
        Promise.allSettled([
          GetAllFormsDataByFormTemplateId(
            self.templateId,
            self.isOnProjectLevel,
            filter,
            self.isAdmin,
            self.user.userId,
            self.fieldValues,
            self.customFilters,
            self.formFieldsTypeMap
          ),
          GetAllformFieldsOfAllVersionsByTemplateId(
            self.templateId,
            self.isOnProjectLevel
          )
        ])
          .then(([res, template]) => {
            res = res.value
            template = template.value
            const formaData = []
            const templateVersions = {}
            for (const version of template.core_form_templates[0]
              ?.template_versions) {
              // const versionId = version.id
              // templateVersions[versionId] = {}
              for (const item of version.template_fields) {
                if (
                  item.caption === 'Created By' ||
                    item.caption === 'Created On' ||
                    item.caption === 'Updated By' ||
                    item.caption === 'Updated On' ||
                    item.caption === 'Due Date' ||
                    item.caption === 'Project Id'
                ) {
                  continue
                } else if (
                  templateVersions[item.field_id] &&
                    templateVersions[item.field_id].includes(item.caption.trim())
                ) {
                } else if (templateVersions[item.field_id]) {
                  templateVersions[item.field_id] += '/ ' + item.caption
                } else {
                  templateVersions[item.field_id] = item.caption
                }
              }
            }
            for (const form of res.core_forms) {
              // const versionId = form.template_version_id
              const data = {
                'Form Id': form.id,
                'Created by':
                    form.created_by_user?.first_name +
                    ' ' +
                    form.created_by_user?.last_name,
                'Created on': `"${form.created_on ? formatFullDate(form.created_on) : '--'
                    }"`,
                'Updated by': form.updated_by_user
                  ? form.updated_by_user?.first_name +
                    ' ' +
                    form.updated_by_user?.last_name
                  : '--',
                'Updated on': `"${form.updated_on ? formatFullDate(form.updated_on) : '--'
                    }"`,
                'Due Date': `"${form.due_date ? formatFullDate(form.due_date) : '--'
                    }"`
              }
              for (const field in templateVersions) {
                const fieldName = templateVersions[field]
                if (
                  fieldName === 'Created By' ||
                    fieldName === 'Created On' ||
                    fieldName === 'Updated By' ||
                    fieldName === 'Updated On' ||
                    fieldName === 'Due Date' ||
                    fieldName === 'Project Id'
                ) {
                  continue
                }
                data[templateVersions[field]] = '--'
              }
              for (const md of form.forms_metadata_by_id) {
                const field = templateVersions[md.field_id]
                data[field] = `"${md.string_value ??
                    md.time_value?.split('+')[0] ??
                    md.int_value ??
                    md.date_value ??
                    md.bool_value ??
                    md.point_value ??
                    md.string_value ??
                    '--'
                    }"`
              }
              for (const userData of form.forms_user_lists) {
                //  this is make all  users name  into  single field name
                const field = templateVersions[userData.field_id]
                if (data[field] && data[field] !== '--') {
                  data[field] = `"${data[field].replaceAll('"', '') +
                      ', ' +
                      userData.core_user?.first_name +
                      ' ' +
                      userData.core_user?.last_name
                      }"`
                } else {
                  data[field] = userData.core_user
                    ? `"${userData.core_user?.first_name +
                      ' ' +
                      userData.core_user?.last_name
                      }"`
                    : '--'
                }
              }
              for (const materialData of form.forms_material_lists) {
                //  this is make all  materials into  single field name
                const field = templateVersions[materialData.field_id]
                if (data[field] && data[field] !== '--') {
                  data[field] = `"${data[field].replaceAll('"', '') +
                      ', ' +
                      materialData?.core_material?.material_name +
                      ' (' +
                      materialData.core_material?.custom_material_id +
                      ')'
                      }"`
                } else {
                  data[field] = materialData?.core_material?.material_name
                    ? materialData?.core_material?.material_name +
                      ' (' +
                      materialData.core_material?.custom_material_id +
                      ')'
                    : '--'
                }
              }
              for (const configData of form.forms_config_lists) {
                const field = templateVersions[configData.field_id]
                data[field] = `"${configData.custom_list_value_by_id.name ?? '--'}"`
              }
              for (const company of form.forms_company_lists) {
                const field = templateVersions[company.field_id]
                data[field] = `${company.company_name ?? '--'}`
              }
              for (const tags of form.forms_tag_list) {
                const field = templateVersions[tags.field_id]
                data[field] = data[field] !== '--' ? data[field] : ''
                if (tags.tag.name) {
                  data[field] = `"${data[field].replaceAll('"', '') +
                      (tags.tag.name) +
                      ', '
                      }"`
                }
              }
              for (const file of form.forms_attachments) {
                const field = templateVersions[file.field_id]
                data[field] = data[field] !== '--' ? data[field] : ''
                if (file?.core_attachment?.file_name || file?.core_document?.doc_name) {
                  data[field] = `"${data[field].replaceAll('"', '') +
                      (file?.core_attachment?.file_name || file?.core_document?.doc_name) +
                      ', '
                      }"`
                }
              }

              formaData.push(data)
            }

            arrayToCsv(formaData, [], 'form exports')

            loader.hide()
          })
          .catch((err) => {
            console.log(err)
            loader.hide()
          })
      }
    },
    keyPress (e) {
      if (e instanceof KeyboardEvent && e.code === 'Escape' && this.drawer) {
        this.cancelFilter()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter' && this.changeAssigneeModal && !this.changeAssigneeButtonDisabled) {
        this.reAssignUser()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter' && this.drawer) {
        this.applyFilters()
      }
    },
    allUsers () {
      if (this.companyValue != null) {
        this.selectedAssignee = {}
        if ((this.companyValue === this.openTenantId) && this.isOnProjectLevel) {
          GetUserListByPojIds([this.openProjectId], [1, 4]).then((res) => {
            this.tenantUsers = res.project_user_association
          })
        } else {
          getTenantUsersListwithInvitedUsers(this.companyValue, this.isOnProjectLevel).then((res) => {
            this.tenantUsers = res.tenant_user_association
          })
        }
      }
    },
    companySelected () {
      this.allUsers()
    },
    reAssignUser () {
      if (this.changeAssigneeButtonDisabled) {
        return
      }
      this.changeAssigneeButtonDisabled = true
      const oldUserId = this.existingUserId
      const newUserId = this.selectedAssignee.id
      if (oldUserId === newUserId) {
        alert('Cannot be assigned to the same user.')
        this.changeAssigneeButtonDisabled = false
        return
      }
      reAssignToNewUser(this.companyValue, this.templateId, oldUserId, newUserId, this.status, this.isOnProjectLevel).then((res) => {
        this.getUsersList()
        success('Added new Assignee successfully')
        this.existingUserId = null
        this.selectedAssignee = {}
        this.companyValue = null
        this.status = null
        this.changeAssigneeModal = false
        this.resetChangeAssigneeModal()
        this.setup()
      }).catch(err => {
        this.changeAssigneeButtonDisabled = false
        console.log(err)
        alert('some thing went wrong! please try again')
      })
    },
    removeUser () {
      this.selectedAssignee = {}
    },
    getUsersList () {
      GetAllAssigneesOfATemplate(this.templateId, this.isOnProjectLevel, this.currentProject?.id).then((res) => {
        this.userList = []
        res.forms_user_list.map((item) => {
          this.userList.push(item.core_user)
        })
      })
        .catch(err => {
          console.log(err)
        })
    },
    resetChangeAssigneeModal () {
      this.changeAssigneeButtonDisabled = false
      this.changeAssigneeModal = false
      this.selectedAssignees = []
      this.existingUserId = null
      this.selectedAssignee = {}
      this.companyValue = null
      this.status = null
      this.userSearchKeyword = ''
    },
    // new assignee dropdown disapperas and loses the focus when mouse move out
    handlemousehover () {
      this.open = false
      if (this.$refs.assigneeInput) {
        this.$refs.assigneeInput.blur()
      }
    }
  },
  mounted () {
    if (
      previousPath.includes('/editform') ||
        previousPath.includes('/viewform')
    ) {
      if (this.formFiltersFromStore.pageNumber) {
        this.pageNumber = this.formFiltersFromStore.pageNumber
      }
      if (this.formFiltersFromStore.searchById) {
        this.searchById = this.formFiltersFromStore.searchById
      }
      this.dueDate = this.formFiltersFromStore.dueDate ? this.formFiltersFromStore.dueDate : {}
      this.createdDate = this.formFiltersFromStore.createdOnDate ? this.formFiltersFromStore.createdOnDate : {}
      this.updatedDate = this.formFiltersFromStore.updatedOnDate ? this.formFiltersFromStore.updatedOnDate : {}
      this.selectedAssignees = Array.isArray(this.formFiltersFromStore.selectedAssignees) ? this.formFiltersFromStore.selectedAssignees : []
      this.statusToFilter = this.formFiltersFromStore.status
      this.formFieldsTypeMap = this.formFieldsTypeMapStore
      this.customFilters = this.customFiltersFromStore
    }
    this.setup()
    this.getTemplateData
    document.body.addEventListener('click', this.handleOutsideClick)
  },
  created () {
    this.$store.dispatch('form/getFormTypeList')
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('click', this.handleOutsideClick)
    document.removeEventListener('keydown', this.keyPress)
  }
}
</script>

  <style lang="scss" scoped>
  .btn-2 {
    width: fit-content;
    height: fit-content;
    padding: 6px;
  }
  .form-list {
    padding-left: 10px;

    .disabled {
        pointer-events: none;
        opacity: 0.5;
        cursor: not-allowed;
      }
    &-bar {
      padding-left: 0;
      // border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      .sort {
    height: 2em;
    width: 10rem;
    margin: 2px 10px 0 10px;
    // margin-left: 2px;
    font-size: 14px;
    background-color: var(--brand-light-color);
    line-height: 1;
    border: 1px solid var(--brand-color);
    border-radius: 4px;
    padding: 4px 12px;
    }
      &-title {
        font-size: 16px;
        font-weight: 500;
      }
    }

    &-container {
      margin: 10px 0;
      overflow-y: auto;

      table {

        th,
        td {
          max-width: 100px;
          /* Set your desired max width for each column */
          overflow: hidden;
          /* Prevents content from overflowing */
          text-overflow: ellipsis;
          /* Adds ellipsis (...) for overflowing text */
          white-space: nowrap;
        }
      }
    }

    &__header {
      padding: 1rem;
      border-bottom: 1px solid #e8e8e8;
    }

    &__title {
      font-size: 1.5rem;
      font-weight: 500;
    }

    &__body {
      padding: 1rem;
      position: relative;
    }

    &__selected-user {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 4px;

      &__avatar {
        margin-right: 0.5rem;
      }

      &__name {
        flex: 1;
        white-space: nowrap;
        margin-right: 0.5rem;
      }

      &__action {
        cursor: pointer;

        b {
          font-size: 1rem;
          width: 16px;
          color: #f1282877;
        }
      }
    }

    &__search {
      margin-bottom: 1rem;

      input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #e8e8e8;
        border-radius: 0.25rem;
      }
    }

    &__list {
      width: 89%;
      max-height: 20rem;
      overflow-y: auto;
      position: absolute;
      background: white;
      left: 22px;
      right: 0px;
      z-index: 1;
      top: 149px;
      padding: 10px;
      box-shadow: 2px 2px 4px rgb(0 0 0 / 20%);
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;

      &-no-result {
        padding: 1rem;
        text-align: center;
      }

      &-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e8e8e8;
        cursor: pointer;

        &:hover {
          background: #f8f8f8;
        }
      }
    }
  }
  .status-display {
  display: flex;
  align-items: center;
  gap: 2px;
}

.status-icon {
  // padding-top: 2px;
  width: 15px;
  height: 15px;
}
  .status {
    font-size: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: fit-content;
    padding: 3px 4px;
    border-radius: 3px;
    background-color: orange;
    &-inprogress {
        background-color: #fff3cd; /* Light yellow */
        color: #856404; /* Dark yellow */
    }
  }
 .dashboard-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: flex-start;
  align-items: center;
  padding: 10px 0;
  box-sizing: border-box;
}

.total, .status-card {
  height: 120px;
  flex: 1 1 calc((100% - 48px) / 5);
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.total {
  background-color: #ede4f7;
  border: 2px solid #7a4ca3;

  label {
    font-size: 13px;
    font-weight: 400;
    color: #5e3d7a;
    margin-bottom: 6px;
  }
}

.status-image {
  width: 28px;
  height: 28px;
  margin-bottom: 6px;
}

.status-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.status-value span:first-child {
  font-weight: 700;
  font-size: 16px;
}

.status-value span:last-child {
  font-weight: 400;
  font-size: 13px;
  margin-top: 3px;
}

/* Media Queries */
@media (max-width: 900px) {
  .total, .status-card {
    flex: 1 1 calc((100% - 24px) / 2);
    height: 110px;
  }
}
  /* Small-height screens */
  @media screen and (max-height: 799px) {
  }

@media (max-width: 500px) {
  .dashboard-container {
    flex-direction: column;
    gap: 12px;
  }

  .total, .status-card {
    width: 100%;
    flex: none;
    height: 110px;
  }
}
  .more {
    &-button {
    cursor: pointer;
    border-radius: 50%;
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
  &-options {
    position: absolute;
    top: 100%;
    right: -10px;
    width: 140px;
    background-color: var(--white);
    border-radius: 5px;
    padding: 10px 2px;
    filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
    z-index: 1;
    &:before {
      content: "";
      position: absolute;
      top: -10px;
      right: 10px;
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-bottom: 10px solid var(--white);
    }
  }
  &-option {
      &-option {
    display: flex;
    align-items: center;
    padding: 5px 8px;
    cursor: pointer;
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
    img {
      width: 20px;
      margin-right: 8px;
    }
    span {
      font-size: 14px;
      font-weight: 500;
      line-height: 1;
      color: var(--text-color);
    }
  }
  }
}
.form-table {
  max-height: calc(100vh - 370px);
  border-radius: 10px;
}
  .drawer {
    position: fixed;
    margin-top: 20px;
    background-color: #f8f4f0;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    border-radius: 5px;
    max-width: 80%;
    width: 400px;
    overflow: auto;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
  }

  .custom-filters {
    width: 85%;
  }

  .avatar-container {
    display: flex;
    align-items: center;
  }

  .avatar-container>* {
    margin-right: 5px;
    /* Adjust the margin between avatars */
  }

  .assignees-box {
    display: flex;
    align-items: center;
    background-color: var(--brand-color);
    padding: 0.4rem;
    padding-top: 0.3rem;
    font-size: small;
    border-radius: 0.4rem;
  }

  .drawer.open {
    opacity: 1;
    visibility: visible;
  }

  .pagination-footer {
    width: 100%;
    display: flex;
    justify-content: space-between;

    &-total {
      margin-top: 16px;
      display: flex;
      background: #F1F3F5;
      padding: 0.5rem 0.5rem 0.5rem;
      border: 1px solid #F1F3F5;
      border-radius: 0.2em;
      width: fit-content;
      padding-inline: 10px;
      margin-left: auto;
    }
  }

  .input-group-ind {
    color: var(--text-color);
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 1rem;

    input,
    select,
    textarea {
      border: 1px solid #3b3b3b77;
      display: block;
      width: 100%;
      border-radius: 0.285em;
      font-size: 1em;
      padding: 0.85em;
      background-color: transparent;

      &::placeholder {
        color: var(--text-color);
        opacity: 0.5;
      }

      &:focus {
        box-shadow: 0 0 0 1px var(--brand-color-1);
      }
    }

    label {
      display: block;
      width: 100%;
      font-size: 1em;
      margin-bottom: 0.2em;
    }
  }
  .form-user-bedge {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #dbd8d8;;
    padding: 5px 10px;
    border-radius: 5px;
    margin-top: 5px;
    .form-user-bedge__name {
      font-size: 12px;
      font-weight: 600;
      color: #000000;
    }
    .form-user-bedge__email {
      font-size: 12px;
      color: #000000;
    }
    .form-user-bedge__action {
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .form-user-search{
    padding:1.1em;
  }
  .addfilter {
    position: relative;
    overflow: visible;
    &-badge {
      position: absolute;
        top: 0;
        right: 10px;
        transform: translate(50%, -50%);
        border-radius: 50%;
        background-color:var(--brand-color-1);
        z-index: 2;
        padding: 3px;
    }
    &-ping {
        animation: ping 1.5s ease-in-out infinite both;
        padding: 10px;
        background-color:black;
        border-radius: 50%;
        transform: translate(50%, -50%);
        position: absolute;
        top: 0;
        right: 10px;
        z-index: 1;
      }
  }
  .configList {
    position: absolute;
    background-color: #fff;
    border: 1px solid rgb(179, 179, 179);
    border-radius: 0.3em;
    z-index: 1;
    width: 36%;
    max-height: 36%;
    overflow-y: auto;
    padding: 10px 14px;
    text-align: left;
    white-space: nowrap;
    overflow: auto;
    text-overflow: ellipsis;
  }
  @keyframes ping {
    0% {
      transform: translate(50%, -50%);
      padding: 5px;
      opacity: 1;
    }
    100% {
      transform: translate(50%, -50%);
      padding: 18px;
      opacity: 0;
    }
  }
  </style>
