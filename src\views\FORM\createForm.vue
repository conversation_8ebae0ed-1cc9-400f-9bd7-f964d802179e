<template>
  <div class="create-form fh">
    <div class="fh center" v-if="loading">
      <loading-circle />
    </div>
    <div v-else class="fh">
      <div class="create-form--nav v-center space-between">
        <h2 class="weight-500 xxl v-center">
          <img
            src="~@/assets/images/icons/arrow-back.svg"
            width="30"
            @click="goBack"
          />
          {{ formTemplateData.name }}
        </h2>
        <div class="v-center flex-end py-3">
          <button class="btn btn-black mx-2" @click="goBack" @keypress="goBack">
            Cancel
          </button>
          <!--  parentFormId is there then no need of draft, its reopned form  -->
          <button v-if="!parentFormId" class="btn mr-2" @click="saveAsDraft">
            Save as draft
          </button>
          <button class="btn btn" @click="checkEmptyUserStepAndSubmit()" :disabled="stepWithNoUsers?.length > 0">Submit</button>
        </div>
      </div>
      <div class="create-form--maincontainer">
        <div class="create-form--container">
          <div class="create-form--elements">
            <div class="create-form-autogenerated" v-if="!materialId">
               <div v-if="formData.sequence_value" class="autogenerated">
                   <span class="label">Sequence ID:</span>
                   <span class="value">
                      {{ formData.sequence_value }}
                   </span>
                </div>
              <template v-for="(ele, index) in templateField">
                <div
                  :key="index"
                  v-if="ele.autogenerated && ele.field_name !== 'project_id'"
                >
                  <span class="label">{{ ele.caption }}:</span>
                  <span class="value">{{ "--" }}</span>
                </div>
              </template>
            </div>
            <div class="create-form-autogenerated" v-else>
              <div>
                <span class="label">Material name:</span>
                <span class="value">{{ $route.query.name }}</span>
              </div>
              <div>
                <span class="label">Material id:</span>
                <span class="value">{{ $route.query.id }}</span>
              </div>
              <div>
                <span class="label">Plm id:</span>
                <span class="value">{{ $route.query.plm }}</span>
              </div>
              <div>
                <span class="label">UOM:</span>
                <span class="value">{{ $route.query.uom }}</span>
              </div>
            </div>
            <div>
           <part-id-preview :components="partIdRules" v-if="partIdRules.length && !formData.sequence_value" @update="updatePartIdValues" title="Sequence Id Preview"/>
          </div>
            <div
              :class="{
                dateComponent: true,
                'form-input': true,
                'form-input--required': true,
              }"
            >
              <label>Due Date:</label>
              <input
                v-model="dueDate"
                type="date"
                placeholder="Search"
                :min="currentDate"
              />
            </div>
            <template v-for="(ele, index) in templateField">
              <component
                v-if="
                  !ele.autogenerated &&
                  (ele.form_field.key !== 'BOM' || isOnProjectLevel)
                "
                :ref="ele.field_id"
                :key="index"
                :is="ele.form_field.key + '_COMPONENT'"
                :data="ele"
                :value="formValueData[ele.field_id]"
                mode="CREATE"
              />
            </template>
          </div>
        </div>
        <div class="create-form--sideBar" v-if="(formTemplateData.id && wftStartStages?.length) || parentFormId">
          <div class="tab-headers" v-if="parentFormId" >
      <div v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-header', { active: selectedTab === tab.id }]"
        @click="selectedTab = tab.id"
      >
        {{ tab.name }}
      </div>
    </div>
    <div v-if="(selectedTab === config.FORM_TAB_STATUS.REVISION_HISTORY || selectedTab ===  config.FORM_TAB_STATUS.HISTORY) && parentFormId" class="tab-body-parent">
      <version-history :rootFormId="formData.root_parent_id" @selectedVersion="getFormData" :formId="formData.id" :viewOnly="true" :selectedTab="selectedTab"/>
    </div>
          <div v-if="selectedTab === config.FORM_TAB_STATUS.WORKFLOW && formTemplateData.id && wftStartStages?.length">
          <div class="create-form-step-selection  "  v-if="wftStartStages?.length > 1 &&  !selectedStartingStep">
            <h4 class="mb-4 v-center space-between">
              Select start step for the workFlow
              <router-link :to="`/workflows/${workflowData?.typeName}/?tid=${workflowData.id}`" target="_blank">
              <button class="btn mr-3 v-center " v-overflow-tooltip="'View workflow'">
                  <img
                    src="~@/assets/images/icons/workflows.svg"
                    class="mx-2"
                    width="20px"
                    height="20px"
                    alt=""
                  />
                </button>
              </router-link>
            </h4>
            <div class="create-form-step-selection-list">
              <div class="create-form-step-selection-item" v-for="stage in wftStartStages" :key="stage.id" @click="addSelectedStep(stage)">
                {{ stage.name }}
              </div>
            </div>
          </div>
            <div class="activeStep" v-else>
              <div class="space-between v-center my-2">
                <h3 class="weight-500 xl v-center ">
          <img v-show="wftStartStages?.length > 1"
            src="~@/assets/images/icons/arrow-back.svg"
            width="20"
            @click="selectedStartingStep = null"
            class="mx-2"
          /> Active Step</h3>
          <router-link :to="`/workflows/${workflowData?.typeName}/?tid=${workflowData.id}`" target="_blank">
                <button class="btn mr-3 v-center" v-overflow-tooltip="'View workflow'">
                  <img
                    src="~@/assets/images/icons/workflows.svg"
                    class="mx-2"
                    width="20px"
                    height="20px"
                    alt=""
                  />
                </button></router-link>
              </div>

              <div class="grid-3 w-100">
                <span class="label">Name </span>
                <span>:</span>
                <span class="value elipsis-text">{{selectedStartingStep.name}}</span>
              </div>

              <div class="grid-3 w-100">
                <span class="label">Duration </span>
                <span>:</span>
                <span class="value">{{selectedStartingStep.duration}} hours</span>
              </div>
              <div class="grid-3 w-100" v-if="selectedStartingStep?.core_user_group?.name">
                <span class="label">Department </span>
                <span>:</span>
                <span class="value">{{ selectedStartingStep?.core_user_group?.name?? '--' }}</span>
              </div>
        <!-- user selection startrs -->
      <div v-if="!isExternalCollaborator && prevInstanceusers!==null">
        <user-selection-for-wf
        :selectedStartingStep="selectedStartingStep"
        @handleUserSelection="handleUserSelection"
        :selectedUsers="wFstepUsers"
      />
</div>
<!-- user selection ends -->
            </div>
          </div>
      </div> </div>
    </div>
  </div>
</template>

<script>
import {
  getDetailFormTemplate,
  SaveFormData,
  upadateMaterialForms,
  GetFormDataByFormId,
  reOpenForms,
  getGivenCustomListsData
} from '@/api'
import loadingCircle from '../../components/common/loadingCircle.vue'
// import { formValidationCheck } from './formValidationCheck'
import { alert, success } from '@/plugins/notification'
import { mapGetters } from 'vuex'
import config from '@/config'
import partIdPreview from '@/components/common/partIdPreview.vue'
import timeComponent from '@/components/form/elements/timeComponent.vue'
import configurationList from '@/components/form/elements/configurationList.vue'
import fileComponent from '@/components/form/elements/fileComponent.vue'
import dateComponent from '@/components/form/elements/dateComponent.vue'
import longTextComponent from '@/components/form/elements/longTextComponent.vue'
import numberComponent from '@/components/form/elements/numberComponent.vue'
import switchComponent from '@/components/form/elements/switchComponent.vue'
import textComponent from '@/components/form/elements/textComponent.vue'
import companyComponent from '@/components/form/elements/companyComponent.vue'
import userComponent from '@/components/form/elements/userComponent.vue'
import multiUserComponent from '@/components/form/elements/multiUserComponent.vue'
import materialComponent from '@/components/form/elements/materialComponent.vue'
import locationComponent from '@/components/form/elements/locationComponent.vue'
import productCodeComponent from '@/components/form/elements/productCodeComponent.vue'
import bomComponent from '@/components/form/elements/bomComponent.vue'
import tagsComponent from '@/components/form/elements/tagsComponent.vue'
import documentsComponent from '@/components/form/elements/documentsComponent.vue'
import userSelectionForWf from '@/components/form/userSelectionForWf.vue'
import { GetFormValueMap } from '@/helper/formValue.js'
import versionHistory from './versionHistory.vue'
import Loader from '@/plugins/loader'
import { getAllStepAssignees, getWorkflowStepWithUsers } from '@/api/apis/workflows'
import confirmationDialog from '@/plugins/confirmationDialog'

export default {
  components: {
    loadingCircle,
    BOOLEAN_COMPONENT: switchComponent,
    NUMBER_COMPONENT: numberComponent,
    TEXT_COMPONENT: textComponent,
    LONG_TEXT_COMPONENT: longTextComponent,
    DATE_COMPONENT: dateComponent,
    TIME_COMPONENT: timeComponent,
    CONFIGURATION_LIST_COMPONENT: configurationList,
    ATTACHMENT_COMPONENT: fileComponent,
    COMPANY_COMPONENT: companyComponent,
    USER_COMPONENT: userComponent,
    MULTI_USER_COMPONENT: multiUserComponent,
    MATERIALS_COMPONENT: materialComponent,
    LOCATION_COMPONENT: locationComponent,
    PRODUCT_CODE_COMPONENT: productCodeComponent,
    BOM_COMPONENT: bomComponent,
    TAGS_COMPONENT: tagsComponent,
    DOCUMENTS_COMPONENT: documentsComponent,
    versionHistory,
    userSelectionForWf,
    partIdPreview
  },
  name: 'createForm',
  data () {
    return {
      partIdValues: [],
      partIdRules: [],
      currentDate: new Date().toLocaleDateString('en-CA'),
      templateId: '',
      templateName: '',
      formTemplateData: {},
      loading: false,
      dueDate: '',
      validationErrors: '', // this is for getting valiadtion errors
      workFlowData: null,
      wftStartStages: [],
      selectedStartingStep: null,
      formValueData: {},
      selectedTab: 1,
      formData: {},
      parentFormId: this.$route.params.parentFormId,
      stepWithNoUsers: null,
      tabs: [
        {
          id: 1,
          name: 'WorkFlow'
        },
        {
          id: 2,
          name: 'Comments'
        },
        {
          id: 3,
          name: 'Version History'
        },
        {
          id: 4,
          name: 'History'
        }
      ],
      config: config,
      wFstepUsers: [], // this is used to store the users selected in the workflow step
      parentInstanceId: null, // only for reopend forms
      prevInstanceusers: this.$route.params.parentFormId ? null : []
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel', 'tenantUsersList', 'currentProject', 'isExternalCollaborator']),
    visibleTabs () {
      return this.tabs.filter(tab => {
        if (tab.name === 'WorkFlow') return this.workflowData.id
        if (tab.name === 'Version History') return this.parentFormId
        return true // Always show "Comments"
      })
    },
    templateField () {
      const activeVersion =
        this.formTemplateData.template_versions.find((item) => item.active) ||
        {}
      return activeVersion.template_fields || []
    },
    materialId () {
      if (this.$route.params?.materialId) {
        return this.$route.params.materialId
      } else {
        return null
      }
    }
  },
  methods: {
    updatePartIdValues (values) {
      this.partIdValues = values
    },
    goBack (e) {
      if (e instanceof KeyboardEvent && e.code !== 'Escape') {
        return
      }
      this.$router.go(-1)
    },
    getFormTemplateData () {
      this.loading = true
      getDetailFormTemplate(this.templateId).then(async (res) => {
        this.formTemplateData = res.core_form_templates_by_pk
        if (res.core_form_templates_by_pk?.core_sequence_id_template?.id_generation_rules) {
          this.partIdRules = res.core_form_templates_by_pk?.core_sequence_id_template?.id_generation_rules
        }
        this.workflowData = { id: res.core_form_templates_by_pk.workflow_template_id, typeName: res.core_form_templates_by_pk.workflow_template?.type_value?.name, versionId: res.core_form_templates_by_pk?.workflow_template?.workflow_versions[0].id }
        this.wftStartStages = res.core_form_templates_by_pk?.workflow_template?.workflow_versions[0]?.workflow_stages
        const customListData = this.findUsedCustomListIds(res.core_form_templates_by_pk.template_versions)
        if (customListData.size > 0) {
          await this.getGivenCustomList([...customListData])
        }
        // to check with any steps without active users
        this.checkUsersAvailabilityWithWorkFlow()
        if (this.wftStartStages?.length === 1) {
          this.selectedStartingStep = this.wftStartStages[0]
        }
        this.loading = false
      })
    },
    saveAsDraft () {
      const formValue = {}
      const form = this.$refs
      this.validationErrors = []
      if (this.dueDate === '') {
        this.validationErrors.push('Please select due date')
      }
      const inputPayload = []
      Object.keys(form).forEach((key) => {
        if (form[key]?.length && form[key][0]?.data?.autogenerated === false) {
          formValue[key] = form[key][0].componentValue
        }
      })
      Object.keys(formValue).forEach((key) => {
        const value = formValue[key]
        if (formValue[key] !== undefined && formValue[key] !== null) {
          if (formValue[key] !== 0 && formValue[key]?.length !== 0 && !Number.isNaN(value)) {
            inputPayload.push({
              field_id: key,
              value: formValue[key]
            })
          }
        }
      })
      // if (!inputPayload.length) {
      //   this.validationErrors.push('Please fill atleast one field')
      // }
      if (this.validationErrors.length > 0) {
        this.validationErrors.forEach((error) => alert(error))
        return
      }
      const body = [
        {
          templateId: this.templateId,
          dueDate: this.dueDate,
          inputPayload: inputPayload,
          status: 2
        }
      ]
      const loader = new Loader()
      loader.show()
      SaveFormData(body, this.isOnProjectLevel)
        .then((res) => {
          const err = res.failedValidations || res.error || []
          if (err.length) {
            alert(`${err[0]?.field_name}: ${err[0]?.message}`)
          } else if (res.message === 'Unable to save form') {
            alert(res.message)
          } else {
            success('Form saved as Draft')
            this.$router.push(`/form/${this.templateId}`)
          }
        })
        .catch((err) => {
          console.log(err)
          alert('some thing went wrong')
        }).finally(() => {
          loader.hide()
        })
    },
    reOpenForm () {
      const form = this.$refs
      const formValue = {}
      this.validationErrors = []
      Object.keys(form).forEach((key) => {
        this.validateInputData(form[key][0]?.data, form)
        if (form[key].length && form[key][0]?.data?.autogenerated === false) {
          if (form[key][0]?.componentValue !== undefined && form[key][0]?.componentValue !== null) {
            formValue[key] = form[key][0].componentValue
          }
        }
      })
      if (this.dueDate === '') {
        this.validationErrors.push('Please select due date')
      }
      if (this.workflowData.id && !this.selectedStartingStep?.id && this.wftStartStages.length) {
        this.validationErrors.push('Please select starting step for the workflow')
        // this.$refs.workflowStepSelection.style.border = '1px solid rgba(0, 0, 0, 0.7)'
      }
      // checking whether the step has users or not
      if (this.workflowData.id && !this.selectedStartingStep?.core_user_group?.name && !this.wFstepUsers?.length) {
        this.validationErrors.push('Please select users for the next step')
      }
      // checking for first step confirmation  -- end
      if (this.validationErrors.length > 0) {
        this.validationErrors.forEach((error) => alert(error))
        return
      }
      const inputPayload = []
      Object.keys(formValue).forEach((key) => {
        if (formValue[key] !== undefined && formValue[key] !== null) {
          if (formValue[key] !== 0 && formValue[key].length !== 0) {
            inputPayload.push({
              field_id: key,
              value: formValue[key]
            })
          }
        }
      })
      const stepUsers = this.wFstepUsers.map(user => { return ({ user_id: user.user_id, target_tenant_id: user.target_tenant_id, step_id: this.selectedStartingStep?.id }) })
      this.prevInstanceusers.forEach((item) => {
        if (item.step_id === this.selectedStartingStep?.id) {
          return
        }
        stepUsers.push({
          user_id: item.user_id,
          target_tenant_id: item.target_tenant_id,
          step_id: item.step_id
        })
      })
      const body =
        {
          cost_impact: false,
          schedule_impact: false,
          due_date: this.dueDate,
          form_id: this.parentFormId,
          input_payload: inputPayload,
          step_id: this.selectedStartingStep?.id ?? undefined,
          workflowStepAssignees: Array.isArray(this.wFstepUsers) ? stepUsers : []
        }
      const loader = new Loader()
      loader.show()
      reOpenForms(body, this.isOnProjectLevel).then((res) => {
        const err = res.failedValidations || res.error || []
        if (err.length) {
          loader.hide()
          alert(`${err[0]?.message}`)
        } else {
          loader.hide()
          success('Form Updated successfully')
          this.$router.push(
                `/form/${this.templateId}`
          )
        }
      }).catch(() => {
        loader.hide()
        alert('some thing went wrong')
      })
    },
    checkEmptyUserStepAndSubmit () {
      if (this.workflowData?.id && this.stepWithNoUsers?.length > 0) {
        confirmationDialog(
            `${this.stepWithNoUsers?.length} workflow steps have user groups with zero users from this project. Forms will be blocked at these steps until resolved.`,
            (res) => {
              if (res) {
                // this.submitForm()
              }
            }
        )
      } else if (this.parentFormId) {
        this.reOpenForm()
      } else {
        this.submitForm()
      }
    },
    validatePartIdValues () {
      let isValid = true
      for (const value of this.partIdValues) {
        if (!value.value) {
          this.validationErrors.push(`Please enter a value for ${value.caption} in form id`)
          isValid = false
        }
      }
      return isValid
    },
    submitForm () {
      const form = this.$refs
      const formValue = {}
      this.validationErrors = []
      if (this.partIdValues.length) {
        this.validatePartIdValues()
      }
      Object.keys(form).forEach((key) => {
        this.validateInputData(form[key][0]?.data, form)
        if (form[key].length && form[key][0]?.data?.autogenerated === false) {
          if (form[key][0]?.componentValue !== undefined && form[key][0]?.componentValue !== null) {
            formValue[key] = form[key][0].componentValue
          }
        }
      })
      if (this.dueDate === '') {
        this.validationErrors.push('Please select due date')
      }
      // checking for first step confirmation  -start
      if (this.workflowData.id && !this.selectedStartingStep?.id && this.wftStartStages.length) {
        this.validationErrors.push('Please select starting step for the workflow')
        // this.$refs.workflowStepSelection.style.border = '1px solid rgba(0, 0, 0, 0.7)'
      }
      // checking whether the step has users or not
      if (this.workflowData.id && !this.selectedStartingStep?.core_user_group?.name && !this.wFstepUsers?.length) {
        this.validationErrors.push('Please select users for the next step')
      }
      // checking for first step confirmation  -- end
      if (this.validationErrors.length > 0) {
        this.validationErrors.forEach((error) => alert(error))
        return
      }
      // if (formValidationCheck(this.templateField, formValue)) {
      const inputPayload = []
      Object.keys(formValue).forEach((key) => {
        if (formValue[key] !== undefined && formValue[key] !== null) {
          if (formValue[key] !== 0 && formValue[key].length !== 0) {
            inputPayload.push({
              field_id: key,
              value: formValue[key]
            })
          }
        }
      })
      const stepUsers = this.wFstepUsers.map((item) => {
        return {
          user_id: item.user_id,
          target_tenant_id: item.target_tenant_id,
          step_id: this.selectedStartingStep?.id
        }
      })
      const body = [
        {
          templateId: this.templateId,
          dueDate: this.dueDate,
          inputPayload: inputPayload,
          status: 1,
          stepId: this.selectedStartingStep?.id ?? undefined,
          workflowStepAssignees: this.wFstepUsers.length ? stepUsers : undefined
        }
      ]
      if (this.partIdRules.length) {
        const sequenceInput = this.partIdValues.map((value) => {
          return {
            value: value.value,
            delimiter: value.delimiter,
            sequence: value.sequence
          }
        })
        body[0].formSequencePayload = sequenceInput
      }
      const loader = new Loader()
      loader.show()
      SaveFormData(body, this.isOnProjectLevel)
        .then((res) => {
          const formId = res.insertedFormIds[0]
          const err = res.failedValidations || res.error || []
          if (err.length) {
            alert(`${err[0]?.field_name}: ${err[0]?.message}`)
          } else if (res.message === 'Unable to save form') {
            alert(res.message)
          } else {
            // this means you are uploading a material custom field form
            if (this.materialId) {
              upadateMaterialForms(this.materialId, formId)
                .then((res) => {
                  this.$router.go(-1)
                  loader.hide()
                  success('Custom fields are added successfully')
                })
                .catch(() => {
                  loader.hide()
                  alert('some thing went wrong')
                })
            } else {
              loader.hide()
              success('Form saved successfully')
              this.$router.push(
                `/form/viewform/${this.templateId}/${this.templateName}/${formId}`
              )
            }
          }
        })
        .catch(() => {
          loader.hide()
          alert('some thing went wrong')
        })
    },
    validateInputData (formInput, form) {
      if (formInput?.form_field?.key === 'DOCUMENTS') {
        if (
          formInput?.required &&
          (!form[formInput.field_id][0].componentValue ||
          !form[formInput.field_id][0].componentValue.length)
        ) {
          this.validationErrors.push(
            `${formInput.caption} is a mandatory field`
          )
        }
      }
      if (formInput?.form_field?.key === 'USER') {
        if (
          formInput?.required &&
          (!form[formInput.field_id][0].componentValue ||
            !form[formInput.field_id][0].componentValue.length)
        ) {
          this.validationErrors.push(
            `${formInput.caption} is a mandatory field`
          )
        }
      }
      if (formInput?.form_field?.key === 'BOOLEAN') {
        // we are comparing the value to null instead of checking if it is a falsy value cause the boolean component value will false in one case and it is valid value
        if (
          formInput?.required &&
          form[formInput.field_id][0].componentValue === null
        ) {
          this.validationErrors.push(
            `${formInput.caption} is a mandatory field`
          )
        }
        return
      } else if (formInput?.form_field?.key === 'MATERIALS') {
        if (
          formInput?.required &&
          form[formInput.field_id][0].componentValue.length < 1
        ) {
          this.validationErrors.push(
            `${formInput.caption} is a mandatory field`
          )
        }
        return
      } else if (formInput?.form_field?.key === 'TAGS') {
        if (
          formInput?.required &&
          form[formInput.field_id][0].componentValue.length < 1
        ) {
          this.validationErrors.push(
            `${formInput.caption} is a mandatory field`
          )
        }
        return
      } else if (formInput?.form_field.key === 'CONFIGURATION_LIST' && !form[formInput.field_id][0]?.componentValue.length && formInput?.required) {
        this.validationErrors.push(
            `${formInput.caption} is a mandatory field`
        )
      }
      if (formInput?.required && !form[formInput.field_id][0].componentValue) {
        this.validationErrors.push(`${formInput.caption} is a mandatory field`)
      }
    },
    addSelectedStep (stage) {
      this.selectedStartingStep = stage
      this.wFstepUsers = this.prevInstanceusers.filter((item) => item.step_id === stage.id)
    },
    getFormData (form) {
      const updatedFormId = form ? form.id : this.parentFormId
      if (updatedFormId) {
        GetFormDataByFormId(updatedFormId, this.isOnProjectLevel).then(
          (res) => {
            this.formData = res.core_forms_by_pk
            this.parentInstanceId = res?.core_forms_by_pk?.workflow_instance_id || null
            this.formValueData = GetFormValueMap(res.core_forms_by_pk)
            this.dueDate = new Date(this.formData.due_date).toISOString().split('T')[0]
            this.getFormTemplateData()
            // to get all step assignees  from the previous form instances, works only  for reopend forms
            this.getPreviousAllStepAssignees()
          }
        )
      }
    },
    checkUsersAvailabilityWithWorkFlow () {
      // const stepUserMap = {}

      getWorkflowStepWithUsers(this.workflowData.versionId).then((res) => {
        const stepUserMapSet = {}
        res.workflow_stages.forEach((stage) => {
          for (const userData of stage.core_user_group?.core_user_group_members ?? []) {
            if (stepUserMapSet[stage.id]) {
              stepUserMapSet[stage.id].add(userData.user_id)
            } else {
              stepUserMapSet[stage.id] = new Set([userData.user_id])
            }
          }
        })
        // checking for the first step with no users, respected stedid are stored
        const stepWithNoUsers = {}
        if (this.isOnProjectLevel) {
          for (const step in stepUserMapSet) {
            let confirmFlag = false
            for (const user of this.currentProject.project_user_associations) {
              if (stepUserMapSet[step].has(user.user_id)) {
                confirmFlag = true
                break
              }
            }
            if (!confirmFlag) {
              stepWithNoUsers[step] = step
            }
          }
        } else {
          for (const step in stepUserMapSet) {
            let confirmFlag = false
            for (const user of this.tenantUsersList) {
              if (stepUserMapSet[step].has(user.associated_user.id)) {
                confirmFlag = true
                break
              }
            }
            if (!confirmFlag) {
              stepWithNoUsers[step] = step
            }
          }
        }
        this.stepWithNoUsers = Object.keys(stepWithNoUsers)
        if (this.stepWithNoUsers?.length > 0) {
          confirmationDialog(
            `${this.stepWithNoUsers?.length} workflow steps have user groups with zero users from this project. Forms will be blocked at these steps until resolved`,
            (res) => {
              if (res) {
              }
            }
          )
        }
      })
    },
    findUsedCustomListIds (templateVersions) {
      const configIdSet = new Set()
      for (const templateVersion of templateVersions) {
        for (const field of templateVersion.template_fields) {
          if (field.field_type_id === config.FORM_TYPE.CONFIGRATION_LIST) {
            configIdSet.add(field.custom_list_id)
          }
        }
      }
      return configIdSet
    },
    async getGivenCustomList (ids) {
      const customListData = await getGivenCustomListsData(ids)
      const customListMap = {}
      for (const customList of customListData.core_custom_list) {
        customListMap[customList.id] = customList.custom_list_values
      }
      this.$store.dispatch('form/saveCustomListMap', customListMap)
    },
    handleUserSelection (data) {
      this.wFstepUsers = data.value.insert
    },
    // fetch all step assignees  from the previous form instances
    getPreviousAllStepAssignees () {
      if (!this.parentFormId) {
        return
      }
      getAllStepAssignees(this.parentInstanceId).then(res => {
        this.prevInstanceusers = res.instance_step_assignees.map((item) => {
          return {
            user_id: item.user_id,
            target_tenant_id: item.target_tenant_id,
            step_id: item.workflow_step_id
          }
        })
        if (this.selectedStartingStep?.id) {
          this.wFstepUsers = this.prevInstanceusers.filter((item) => item.step_id === this.selectedStartingStep?.id)
        }
      })
        .catch(err => {
          console.log(err)
        })
    }
  },
  mounted () {
    this.getFormData()
    // fetch all step assignees  from the previous form instances
  },
  created () {
    this.templateId = this.$route.params.templateId
    this.templateName = this.$route.params.templateName
    this.getFormTemplateData()
  }
}
</script>

<style lang="scss" scoped>
.create-form {
  background: rgba(255, 196, 103, 0.05);
  &--nav {
    height: 60px;
    margin: -12px;
    padding: 0 20px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
    & input {
      border: var(--border);
      background-color: transparent;
      border-radius: 4px;
      padding: 4px 8px;
      height: 30px;
      width: 200px;
      outline: none;
      &:focus {
        border-color: var(--brand-color);
      }
    }
  }
  &--container {
    padding: 20px;
    max-width: 700px;
    overflow-y: auto;
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
    margin-top: 10px;
    border-radius: 6px;
    background: white;
  }
  &--elements {
    height: -webkit-fill-available;
    overflow-y: auto;
    padding: 4px;
  }
  &-autogenerated {
    background-color: rgba(var(--brand-rgb), 0.2);
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    grid-gap: 10px;
    & .label {
      font-size: 14px;
      color: var(--brand-color-1);
      font-weight: 500;
    }
    & .value {
      margin-left: 6px;
      font-size: 16px;
      color: var(--text-color);
    }
  }
  &--sideBar {
    // padding: 20px;
    margin-top: 9px;
    max-width: 586px;
    height: calc(100%);
    overflow-y: auto;
    border-radius: 6px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    // background: white;
    & .activeStep {
      display: flex;
      flex-direction: column;
      gap: 20px;
      box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
      padding: 20px;
      background: white;
      border-radius: 6px;
    }
  }
  &--maincontainer {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 20px;
    height: calc(100% - 60px);
  }
  &-step-selection{
      box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
      padding: 20px;
      background: white;
      border-radius: 6px;
      margin: 1px;
      &-list{
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;

      }
    &-item{
border: 1px solid rgb(73, 73, 73, .3);
      padding: 10px 20px;
      border-radius: 5px;
      background-color:rgb(126, 172, 248, .6);
      font-size: 12x;
      color: black;
      cursor: pointer;
    }
  }
}
.tabs {
  border: 1px solid #ccc;
  border-radius: 6px;
  overflow: hidden;
  font-family: sans-serif;
}

.tab-headers {
  display: flex;
  background-color: #f1f1f1;
  border-bottom: 1px solid #ccc;
}

.tab-header {
  padding: 12px 18px;
  cursor: pointer;
  flex: 1;
  text-align: center;
  transition: background 0.3s;
}

.tab-header:hover {
  background-color: #e0e0e0;
}

.tab-header.active {
  background-color: #fff;
  font-weight: 500;
  border-bottom: 2px solid var(--brand-color);
}
.grid-3{
  display: grid;
  grid-template-columns: repeat(3, 1fr);

  align-items: center;
  & .label{

  }
  & .value{

  }
}
</style>
