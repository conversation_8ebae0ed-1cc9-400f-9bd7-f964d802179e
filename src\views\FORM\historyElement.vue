<template>
      <div class="history-body-element">
        <div class="history-body-basic">
          <div class="history-body-basic-userdata">
            <div class="history-body-basic-userdata-avatar">{{data.username | avatar<PERSON>har}}</div>
            <div class="flex-column">
              <p class="history-body-basic-userdata-name">{{data.username}}</p>
              <p class="history-body-basic-userdata-email">{{data.email}}</p>
            </div>
          </div>
          <div class="history-body-basic-right">
            <div class="history-body-basic-time">
              <img
          src="~@/assets/images/icons/clock.svg"
          width="13px"
          alt=""
        />{{ data.timestamp | timeFormatter }} <b class="v-center h-100 mb-1">.</b> {{ data.timestamp | dateFormatter }}</div>
            <div class="history-body-basic-type">
              <img v-if="data.state === 'STARTED'" width="12px" src="~@/assets/images/histroyIcons/start_green.svg.svg" alt="" />
              <img v-else-if="data.state === 'TRANSITIONED'" width="12px" src="~@/assets/images/histroyIcons/transition.svg" alt="" />
              <img v-else-if="data.state === 'CLOSED'" width="12px" src="~@/assets/images/histroyIcons/closed_orange.svg" alt="" />
              <img v-else-if="data.state === 'ASSIGNED'" width="12px" src="~@/assets/images/histroyIcons/assignee_blue.svg" alt="" />
              <img v-else-if="data.state === 'INSERTED'" width="12px" src="~@/assets/images/histroyIcons/insert_green.svg" alt="" />
              <img v-else-if="data.state === 'DELETED'" width="12px" src="~@/assets/images/histroyIcons/deleted_red.svg" alt="" />
              <img v-else-if="data.state === 'UPDATED'" width="12px" src="~@/assets/images/histroyIcons/update_purple.svg" alt="" />
              <img v-else-if="data.state === 'CANCELLED'" width="12px" src="~@/assets/images/histroyIcons/cancel_red.svg" alt="" />
              {{data.state}}</div>
          </div>
        </div>
        <div class="history-body-content"> {{data.message}}
        <p  v-if="data?.reason" class="history-body-content-reason">{{data?.reason}} </p>
        </div>
         <div class="history-body-element-indicator"></div>
      </div>
</template>
<script>
export default ({
  name: 'historyElement',
  props: {
    elementColor: { type: String, default: '238, 159, 214' },
    data: { type: Object, default: () => {} }
  },
  filters: {
    avatarChar (username) {
      const afterSplit = username.split(' ')
      if (afterSplit.length === 1) return afterSplit[0][0]
      else if (afterSplit.length === 2) return afterSplit[0][0] + afterSplit[1][0]
      else if (afterSplit.length > 2) { return afterSplit[0][0] + afterSplit[-1][0] }
    },
    timeFormatter (value) {
      if (!value) return ''
      const date = new Date(value)
      const hours = date.getHours()
      const minutes = date.getMinutes()
      // Format hours to 12-hour format
      const formattedHour = hours % 12 || 12
      const ampm = hours >= 12 ? 'PM' : 'AM'
      const formattedMinutes = minutes.toString().padStart(2, '0')
      return `${formattedHour}.${formattedMinutes} ${ampm}`
    },
    dateFormatter (value) {
      return new Date(value).toLocaleDateString('en-US', {
        month: 'short', // Jul
        day: 'numeric' // 20
      }) ?? '--'
    }
  },
  mounted () {
    this.$el.style.setProperty('--element-unique-color', this.data.elementColor)
  },
  watch: {
    'data.state' () {
      this.$el.style.setProperty('--element-unique-color', this.data.elementColor)
    }
  }
})
</script>
<style lang="scss" scoped>
$greyShade-color: #b8b7b7;
.history-body {
--element-unique-color: 238, 159, 214;
    &-element {
      padding: 10px 10px;
      border: 1px solid rgba(var(--element-unique-color), 0.4);
      margin-top: 10px;
      border-radius: 1em;
      position:relative;
      &-indicator{
        position:absolute;
        padding:5px;
        background-color: rgba(var(--element-unique-color), .8);
        border-radius:50%;
        left:0;
     top: 26px;
       transform: translateX(-50%);
      }
    }
    &-basic {
      display:flex;
      justify-content: space-between;
      align-items:center;
      gap: 20px;
      &-userdata {
        display: flex;
        align-items: center;
        gap: 10px;
        &-name {
          font-weight: 400;
          font-size: 0.9em;
        }
        &-email {
          font-weight: 400;
          font-size: 0.8em;
          margin-top: 3px;
          color: $greyShade-color;
        }
        &-avatar {
          border-radius: 50%;
          padding: 20px;
          background-color:rgba(var(--element-unique-color), 0.1);
          width: 2em;
          height: 2em;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1em;
          font-weight: 300;
          color: rgba(var(--element-unique-color));
        }
      }
      &-right {
      display:flex;
      align-items:center;
      gap: 10px;
      }
      &-time {
          color: $greyShade-color;
          font-size: 0.8em;
          display: flex;
          align-items: center;
          gap: 5px;
        }
        &-type {
          all:unset;
          border: 1px solid rgba(var(--element-unique-color), 0.6);
          padding: 0px 10px;
          text-align: center;
          text-decoration: none;
          cursor: pointer;
          border-radius: 16px;
          color:rgba(var(--element-unique-color));
          background-color: rgb(238, 159, 214, 0.1);
          font-size:.85em;
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 5px;
        }
    }
    &-content{
      margin-top:10px;
      font-size: 0.85em;
      background-color: rgb($greyShade-color,.1);
      width:100%;
      text-wrap:wrap;
      padding: 10px;
      border:.4px solid rgb($greyShade-color,.3);
      border-radius:5px;
      opacity: 0.8;
      &-reason{
        font-size: 0.85em;
        color: #554a4a;;
        margin-top: 5px;
        text-wrap:wrap;
      }
    }
}
@media (max-width: 480px) {
  .history-body-basic-right{
    flex-direction: column-reverse;
    gap:0px;
  }
}

/* Tablets (portrait and small tablets: 481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
 .history-body-basic-right{
    flex-direction: column-reverse;
    gap:0px;
  }

}

/* Tablets (standard: 768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Standard tablet styles here */
}

/* Laptops (1025px - 1280px) */
@media (min-width: 1025px) and (max-width: 1280px) {
  /* Laptop styles here */
}

/* Desktops (1281px - 1600px) */
@media (min-width: 1281px) and (max-width: 1600px) {
  /* Desktop styles here */
}

/* Big Screens (1601px and up) */
@media (min-width: 1601px) {
  /* Big screen styles here */
}

</style>
