<template>
  <div
    class="wf-form-step-selection"
    v-if="wftStartStages?.length > 1 && !selectedStartingStep"
  >
    <h4 class="mb-4 v-center space-between">
      Select start step for the workFlow
      <router-link
        :to="`/workflows/${wfTypeName}/?tid=${templateId}`"
        target="_blank"
      >
        <button class="btn mr-3 v-center" v-overflow-tooltip="'View workflow'">
          <img
            src="~@/assets/images/icons/workflows.svg"
            class="mx-2"
            width="20px"
            height="20px"
            alt=""
          />
        </button>
      </router-link>
    </h4>
    <div class="wf-form-step-selection-list">
      <div
        class="wf-form-step-selection-item"
        v-for="stage in wftStartStages"
        :key="stage.id"
        @click="$emit('selectStartingStep', stage)"
      >
        {{ stage.name }}
      </div>
    </div>
  </div>
  <div class="activeStep" v-else>
    <div class="space-between v-center my-2">
      <h3 class="weight-500 xl v-center">
        <img
          v-if="wftStartStages?.length > 1"
          src="~@/assets/images/icons/arrow-back.svg"
          width="20"
          @click="$emit('removeSelection')"
          class="mx-2"
        />
        Active Step
      </h3>
      <router-link
        :to="`/workflows/${wfTypeName}/?tid=${templateId}`"
        target="_blank"
      >
        <button class="btn mr-3 v-center" v-overflow-tooltip="'View workflow'">
          <img
            src="~@/assets/images/icons/workflows.svg"
            class="mx-2"
            width="20px"
            height="20px"
            alt=""
          /></button
      ></router-link>
    </div>

    <div class="grid-3 w-100">
      <span class="label">Name </span>
      <span>:</span>
      <span class="value elipsis-text">{{ selectedStartingStep.name }}</span>
    </div>

    <div class="grid-3 w-100">
      <span class="label">Duration </span>
      <span>:</span>
      <span class="value">{{ selectedStartingStep.duration }} hours</span>
    </div>
    <div
      class="grid-3 w-100"
      v-if="selectedStartingStep?.core_user_group?.name"
    >
      <span class="label">Department </span>
      <span>:</span>
      <span class="value">{{
        selectedStartingStep?.core_user_group?.name ?? "--"
      }}</span>
    </div>
    <!-- user selection startrs -->
 <div v-if="!isExternalCollaborator && prevInstanceusers !== null">
 <user-selection-for-wf
        :selectedStartingStep="selectedStartingStep"
        :selectedUsers="prevInstanceusers"
        @handleUserSelection="handleUserSelection"
      />
</div>
<!-- user selection ends -->
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import userSelectionForWf from '@/components/form/userSelectionForWf.vue'

export default {
  components: {
    userSelectionForWf
  },
  name: 'workflowStartingStpSelection',
  props: {
    wftStartStages: {
      type: Array,
      default: () => []
    },
    workflowData: {
      type: Object,
      default: () => ({})
    },
    selectedStartingStep: {
      type: Object,
      default: null
    },
    wfTypeName: {
      type: String,
      default: ''
    },
    templateId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      prevInstanceusers: []
    }
  },
  computed: {
    ...mapGetters(['isExternalCollaborator'])
  },
  methods: {
    handleUserSelection (data) {
      this.$emit('handleUserSelection', data)
    }
  }
}
</script>
<style lang="scss" scoped>
.wf-form-step-selection {
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
  padding: 20px;
  background: white;
  border-radius: 6px;
  margin: 1px;
  &-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
  }
  &-item {
    border: 1px solid rgb(73, 73, 73, 0.3);
    padding: 10px 20px;
    border-radius: 5px;
    background-color: rgb(126, 172, 248, 0.6);
    font-size: 12x;
    color: black;
    cursor: pointer;
  }
}
.activeStep {
      display: flex;
      flex-direction: column;
      gap: 20px;
      box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
      padding: 20px;
      background: white;
      border-radius: 6px;
      margin: 2px 4px 4px 4px;
    }
.grid-3{
  display: grid;
  grid-template-columns: repeat(3, 1fr);

  align-items: center;
  & .label{

  }
  & .value{

  }
}
</style>
